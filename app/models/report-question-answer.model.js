const mongoose = require('mongoose');

const ReportQuestionAnswer = mongoose.Schema(
  {
    title: [
      {
        value: {
          type: String,
        },
        isActive: {
          type: Boolean,
          default: true,
        },
        isPrintable: {
          type: Boolean,
          default: true,
        },
        isRequired: {
          type: Boolean,
          default: true,
        },
      },
    ],
    reportQuestion: {
      type: mongoose.Types.ObjectId,
      ref: 'report-question',
    },
    parameterType: {
      type: mongoose.Types.ObjectId,
      ref: 'parameter-type',
    },
    report: {
      type: mongoose.Types.ObjectId,
      ref: 'report',
    },
    option: [
      {
        title: {
          type: String,
        },
        isActive: {
          type: Boolean,
          default: true,
        },
      },
      {
        default: null,
      },
    ],
    range: {
      min: {
        type: Number,
        default: null,
      },
      max: {
        type: Number,
        default: null,
      },
    },
    numberOfAnswers: {
      type: Number,
      default: 1,
    },
    sortOrder: {
      type: Number,
      default: 0,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('report-question-answer', ReportQuestionAnswer);
