const mongoose = require('mongoose');

const UserReportAnswer = mongoose.Schema(
  {
    reportQuestion: {
      type: mongoose.Types.ObjectId,
      ref: 'report-question',
    },
    reportQuestionAnswer: {
      type: mongoose.Types.ObjectId,
      ref: 'report-question-answer',
    },
    userReport: {
      type: mongoose.Types.ObjectId,
      ref: 'user-report',
    },
    report: {
      type: mongoose.Types.ObjectId,
      ref: 'report',
    },
    answers: [
      {
        answerTitleId: {
          type: mongoose.Types.ObjectId,
          default: null,
        },
        answer: {
          type: String,
          default: '',
        },
        isActive: {
          type: Boolean,
          default: true,
        },
        isPrintable: {
          type: Boolean,
          default: true,
        },
      },
    ],
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('user-report-answer', UserReportAnswer);
