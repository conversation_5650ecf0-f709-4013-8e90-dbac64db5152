const mongoose = require('mongoose');

const Member = new mongoose.Schema(
  {
    project: {
      type: mongoose.Types.ObjectId,
      ref: 'project',
    },
    function: {
      type: mongoose.Types.ObjectId,
      ref: 'function',
      default: null,
    },
    user: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    isApprover: {
      type: Boolean,
      default: false,
    },
    showOnDpr: {
      type: Boolean,
      default: false,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('member', Member);
