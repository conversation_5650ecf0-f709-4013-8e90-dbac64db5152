const mongoose = require('mongoose');

const NotInList = mongoose.Schema(
  {
    memberName: {
      type: String,
      default: '',
    },
    functionName: { type: String, default: '' },
    shift: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
      required: true,
    },
    project: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
      required: true,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
      required: true,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('not-in-list', NotInList);
