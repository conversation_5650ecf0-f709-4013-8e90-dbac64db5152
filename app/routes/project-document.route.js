// library
const express = require('express');
const routes = express.Router();

// Validator
const validator = require('../validators/project-document.validator');

// middleware
const { verifyToken, authAccount } = require('../middlewares/auth.middleware');
const { validate } = require('../middlewares/validate.middleware');

// controller
const projectDocumentController = require('../controllers/project-document.controller');

// Create Project Document
routes.post(
  '',
  verifyToken,
  authAccount,
  validator.createProjectDocumentValidationRule(),
  validate,
  projectDocumentController.createProjectDocument
);

// Get Project Document List
routes.get('', verifyToken, authAccount, validate, projectDocumentController.getProjectDocuments);

routes.get(
  '/:dprId/summary',
  verifyToken,
  authAccount,
  validate,
  projectDocumentController.getProjectDocumentsSummary
);

// Update Project Document
routes.patch(
  '/:id',
  verifyToken,
  authAccount,
  validator.updateProjectDocumentValidationRule(),
  validate,
  projectDocumentController.updateProjectDocument
);

// Delete Project Document
routes.delete(
  '/:id',
  verifyToken,
  authAccount,
  validate,
  projectDocumentController.deleteProjectDocument
);

module.exports = routes;
