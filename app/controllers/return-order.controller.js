const mongoose = require('mongoose');

// Services
const returnOrderService = require('../services/return-order.service');
const returnOrderHistoryService = require('../services/return-order-history.service');
const equipmentOrderHistoryService = require('../services/equipment-order-history.service');
const equipmentService = require('../services/equipment.service');
const inventoryHistoryService = require('../services/inventory-history.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const commonUtils = require('../utils/common.utils');
const { transactionOption } = require('../utils/json-format.utils');
const HTTP_STATUS = require('../utils/status-codes');

/**
 * Get PM Orders Equipment List
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getPMOrderEquipment = async (req, res) => {
  try {
    let page = req.query.page ? req.query.page : 0;
    let perPage = req.query.perPage ? req.query.perPage : 100;
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;
    let filterData = {
      account: req.userData.account,
      deletedAt: null,
    };

    if (req.query.status === null || req.query.status === undefined) {
      filterData['$or'] = [{ status: 'check-in' }, { returnStatus: 'partially-returned' }];
    } else {
      filterData.status = req.query.status;
    }

    let otherFilter = null;
    if ('project' in req.query) {
      if (!commonUtils.isValidId(req.query.project)) {
        return res.status(400).json(responseUtils.errorResponse(constantUtils.INVALID_PROJECT_ID));
      }
      otherFilter = {
        '_id.projectId': commonUtils.toObjectId(req.query.project),
        ...('priceType' in req.query && { equipmentPriceType: req.query.priceType }),
      };
    }

    if ('search' in req.query) {
      let { search } = req.query;
      otherFilter = {
        $and: [
          {
            $or: [
              { equipmentName: { $regex: search, $options: 'i' } },
              { serialNumber: { $regex: search, $options: 'i' } },
              { equipmentNumber: { $regex: search, $options: 'i' } },
              { equipmentType: { $regex: search, $options: 'i' } },
              { equipmentCategory: { $regex: search, $options: 'i' } },
            ],
          },
          otherFilter,
        ],
      };
    }

    const pmOrderEquipment = await returnOrderService.getOrderEquipmentList(
      filterData,
      page,
      perPage,
      sort,
      otherFilter
    );

    let updatedPmOrderEquipment = [];
    for (let pmorderItem of pmOrderEquipment) {
      if (pmorderItem?.returnOrder && pmorderItem.returnOrder.length > 0) {
        const returnOrderLength = pmorderItem.returnOrder.length;
        const getReturnOrderHistory = await returnOrderHistoryService.getSingleReturnOrderHistory({
          returnOrder: pmorderItem.returnOrder[returnOrderLength - 1].returnOrder,
          equipment: pmorderItem._id.equipmentId,
          status: 'pre-return',
          account: req.userData.account,
          deletedAt: null,
        });

        if (!getReturnOrderHistory) {
          updatedPmOrderEquipment.push(pmorderItem);
        }
      } else {
        updatedPmOrderEquipment.push(pmorderItem);
      }
    }

    return res
      .status(200)
      .json(
        responseUtils.successResponse(
          constantUtils.GET_PM_ORDER_EQUIPMENT_LIST,
          updatedPmOrderEquipment
        )
      );
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Pre checkout equipment
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.preCheckoutEquipment = async (req, res) => {
  const session = await mongoose.startSession();
  const transactionOptions = {
    readConcern: { level: 'snapshot' },
    writeConcern: { w: 'majority' },
    readPreference: 'primary',
  };
  try {
    let reqData = req.body;
    const errorMsg = await this.validateRequestData(reqData);

    if (errorMsg) {
      return res.status(400).json(responseUtils.errorResponse(errorMsg));
    }

    session.startTransaction(transactionOptions);

    let exist = await returnOrderService.getSingleReturnOrderDataByFilter({
      project: reqData[0].projectId,
      status: 'pre-return',
      account: req.userData.account,
      deletedAt: null,
    });

    let returnOrderId = null;
    if (!exist) {
      const createReturnOrderData = await returnOrderService.createReturnOrder(
        {
          project: reqData[0].projectId,
          account: req.userData.account,
          createdBy: req.userData._id,
          createdAt: new Date(),
        },
        session
      );
      returnOrderId = createReturnOrderData._id;
    } else {
      returnOrderId = exist._id;
    }

    if (returnOrderId !== null) {
      await this.processEquipment(
        reqData,
        returnOrderId,
        req.userData._id,
        req.userData.account,
        session
      );
    } else {
      await session.abortTransaction();
      session.endSession();
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.RETURN_ORDER_NOT_CREATED));
    }
    await session.commitTransaction();
    session.endSession();
    return res.status(200).json(responseUtils.successResponse(constantUtils.READY_TO_CHECKOUT));
  } catch (error) {
    await session.abortTransaction();
    session.endSession();
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Return Equipment Order
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.returnEquipmentOrder = async (req, res) => {
  let session;
  session = await mongoose.startSession();
  const transactionOptions = {
    readConcern: { level: 'snapshot' },
    writeConcern: { w: 'majority' },
    readPreference: 'primary',
  };
  try {
    let reqData = req.body;

    if (reqData.length === 0) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.RETURN_ORDER_BODY_EMPTY));
    }

    if (!reqData[0].returnOrder || reqData[0].returnEquipmentData.length === 0) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.RETURN_CART_IS_EMPTY));
    }

    let returnOrderNumber = commonUtils.generateOrderNumber(10, 'return');

    session.startTransaction(transactionOptions);

    for (reqData of reqData) {
      await returnOrderService.updateReturnOrder(
        reqData.returnOrder,
        {
          orderNumber: returnOrderNumber,
          status: 'return',
          updatedBy: req.userData._id,
          updatedAt: new Date(),
          ...(reqData.remark ? { pmRemark: reqData.remark } : {}),
        },
        session
      );

      for (let returnEquipmentData of reqData.returnEquipmentData) {
        let updateOrderHistory = await returnOrderHistoryService.updateReturnOrderHistory(
          returnEquipmentData.id,
          {
            status: 'return',
            pmDispatchQuantity: returnEquipmentData.pmDispatchQuantity,
            updatedBy: req.userData._id,
            updatedAt: new Date(),
          },
          session
        );

        if (updateOrderHistory) {
          let updateData = await returnOrderHistoryService.getSingleReturnOrderHistory({
            _id: returnEquipmentData.id,
            account: req.userData.account,
            deletedAt: null,
          });
          await inventoryHistoryService.prepareInventoryHistoryAndCreate(
            updateOrderHistory.equipment,
            'return',
            'return',
            `${updateData.project.title} -> ${updateData.equipment.warehouse.name}`,
            returnOrderNumber,
            updateOrderHistory.pmDispatchQuantity,
            '',
            reqData.returnOrder,
            req.userData.account,
            req.userData._id,
            session
          );
        }

        let equipmentHisData =
          await equipmentOrderHistoryService.getEquipmentOrderHistoryOneByFilter({
            returnOrder: {
              $elemMatch: {
                returnOrder: commonUtils.toObjectId(reqData.returnOrder),
              },
            },
            equipment: updateOrderHistory.equipment,
          });

        await equipmentOrderHistoryService.updateEquipmentHistoryByFilter(
          {
            returnOrder: {
              $elemMatch: {
                returnOrder: commonUtils.toObjectId(reqData.returnOrder),
              },
            },
            equipment: updateOrderHistory.equipment,
            deletedAt: null,
          },
          {
            'returnOrder.$.quantity': returnEquipmentData.pmDispatchQuantity,
            updatedBy: req.userData._id,
            pmDispatchQuantity:
              equipmentHisData.pmDispatchQuantity + returnEquipmentData.pmDispatchQuantity,
            updatedAt: new Date(),
          },
          session
        );
        await session.commitTransaction();
        session.endSession();

        session = await mongoose.startSession();
        session.startTransaction(transactionOptions);

        let equipHisData = await equipmentOrderHistoryService.getEquipmentOrderHistoryOneByFilter({
          returnOrder: {
            $elemMatch: {
              returnOrder: commonUtils.toObjectId(reqData.returnOrder),
            },
          },
          equipment: updateOrderHistory.equipment,
        });

        const totalQuantity = equipHisData.returnOrder.reduce(
          (total, order) => total + order.quantity,
          0
        );
        let updateEquipHis = {
          returnStatus:
            equipHisData.pmReceivedQuantity == totalQuantity ? 'return' : 'partially-returned',
          pmDispatchQuantity: totalQuantity,
          updatedBy: req.userData._id,
          updatedAt: new Date(),
        };

        await equipmentOrderHistoryService.updateEquipmentHistory(
          {
            returnOrder: {
              $elemMatch: {
                returnOrder: commonUtils.toObjectId(reqData.returnOrder),
              },
            },
            equipment: updateOrderHistory.equipment,
            deletedAt: null,
          },
          updateEquipHis,
          session
        );
      }
    }

    await session.commitTransaction();
    session.endSession();

    return res.status(200).json(responseUtils.successResponse(constantUtils.ORDER_HAS_BEEN_RETURN));
  } catch (error) {
    await session.abortTransaction();
    session.endSession();
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.getEquipmentDataByQR = async (req, res) => {
  try {
    const { projectId, qrCode } = req.params;
    const { status } = req.query;

    // check equipment QR code
    const getEquipment = await equipmentService.findQRCode(qrCode);

    if (getEquipment.length === 0) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.EQUIPMENT_NOT_FOUND));
    }
    const priceType = getEquipment[0]?.equipmentType?.quantityType?.priceType;
    if (priceType !== 'rental') {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.INVALID_QUANTITY_TYPE));
    }

    let filterData = {
      equipment: getEquipment[0]._id,
      status: { $in: ['partially-check-in', 'check-in', 'partially-in-stock', status] },
      deletedAt: null,
    };

    // check equipment is in order
    const getOrderEquipment = await returnOrderService.getOrderEquipmentList(
      filterData,
      null,
      null,
      1,
      {
        '_id.projectId': commonUtils.toObjectId(projectId),
      }
    );

    if (getOrderEquipment.length === 0) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.SCANNED_EQUIPMENT_NOT_IN_ORDER));
    }
    let getOrderEquipmentCart;
    // check equipment is in return cart
    for (let orderEquipment of getOrderEquipment) {
      if (
        orderEquipment.returnStatus == '' ||
        orderEquipment.returnStatus == 'partially-returned'
      ) {
        getOrderEquipmentCart = orderEquipment;
      }
    }

    if (!getOrderEquipmentCart) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_NOT_IN_INVENTORY));
    }

    if (getOrderEquipmentCart.returnOrder.length > 0) {
      const returnOrderLength = getOrderEquipmentCart.returnOrder.length;
      const getReturnOrderHistory = await returnOrderHistoryService.getSingleReturnOrderHistory({
        returnOrder: getOrderEquipmentCart.returnOrder[returnOrderLength - 1].returnOrder,
        equipment: getEquipment[0]._id,
        status: 'pre-return',
        account: req.userData.account,
        deletedAt: null,
      });

      if (getReturnOrderHistory) {
        return res
          .status(400)
          .json(responseUtils.errorResponse(constantUtils.EQUIPMENT_ALREADY_IN_RETURN_CART));
      }
    }

    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.GET_EQUIPMENT, getOrderEquipmentCart));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.getReturnOrderEquipment = async (req, res) => {
  try {
    let { projectId } = req.params;

    if (!commonUtils.isValidId(projectId)) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.INVALID_PROJECT_ID));
    }

    let page = req.query.page ? req.query.page : 0;
    let perPage = req.query.perPage ? req.query.perPage : 100;
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;
    let filterData = {
      project: commonUtils.toObjectId(projectId),
      status: req.query.status ?? 'pre-return',
      account: req.userData.account,
      deletedAt: null,
    };

    const pmOrderEquipment = await returnOrderHistoryService.getReturnOrderEquipmentList(
      filterData,
      page,
      perPage,
      sort
    );

    return res
      .status(200)
      .json(
        responseUtils.successResponse(constantUtils.GET_PM_ORDER_EQUIPMENT_LIST, pmOrderEquipment)
      );
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.removeReturnOrderEquipment = async (req, res) => {
  const session = await mongoose.startSession();
  const transactionOptions = {
    readConcern: { level: 'snapshot' },
    writeConcern: { w: 'majority' },
    readPreference: 'primary',
  };
  try {
    const { id } = req.params;

    let exist = await returnOrderHistoryService.getReturnOrderEquipmentList(
      {
        _id: commonUtils.toObjectId(id),
        account: req.userData.account,
        deletedAt: null,
      },
      0,
      1,
      1
    );

    if (exist.length === 0) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.EQUIPMENT_NOT_FOUND));
    }

    session.startTransaction(transactionOptions);

    const removeData = await returnOrderHistoryService.removeReturnOrderHistory(id, session);

    if (removeData) {
      let hisData = await equipmentOrderHistoryService.getEquipmentHistory({
        returnOrder: {
          $elemMatch: {
            returnOrder: commonUtils.toObjectId(removeData.returnOrder),
          },
        },
        equipment: removeData.equipment,
      });

      if (hisData?.returnOrder) {
        hisData.returnOrder = hisData.returnOrder.filter(
          element => !element.returnOrder.equals(commonUtils.toObjectId(removeData.returnOrder))
        );

        await equipmentOrderHistoryService.updateEquipmentHistoryByFilter(
          {
            returnOrder: {
              $elemMatch: {
                returnOrder: commonUtils.toObjectId(removeData.returnOrder),
              },
            },
            equipment: removeData.equipment,
          },
          {
            returnOrder: hisData.returnOrder,
            returnStatus: hisData.pmDispatchQuantity ? 'partially-returned' : '',
          },
          session
        );
      }

      const getFilterData = await returnOrderHistoryService.getReturnOrderEquipmentList(
        {
          returnOrder: commonUtils.toObjectId(removeData.returnOrder),
        },
        0,
        1,
        1
      );

      if (getFilterData.length === 0) {
        await returnOrderService.removeReturnOrder(removeData.returnOrder, session);
      }
    }
    await session.commitTransaction();
    session.endSession();
    return res.status(200).json(responseUtils.successResponse(constantUtils.REMOVE_CART_EQUIPMENT));
  } catch (error) {
    await session.abortTransaction();
    session.endSession();
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.getPMReturnOrder = async (req, res) => {
  try {
    let page = req.query.page ? req.query.page : 0;
    let perPage = req.query.perPage ? req.query.perPage : 100;
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;
    let filterData = {
      status: req.query.status ?? { $in: ['return', 'rejected'] },
      account: req.userData.account,
      deletedAt: null,
    };

    // Add project filter if project status is provided
    if ('projectStatus' in req.query) {
      filterData = await commonUtils.filterProjectStatus(
        req.query.projectStatus,
        req.userData.account,
        filterData
      );
    }

    delete filterData?.projectStatus;

    const pmReturnOrder = await returnOrderService.pmReturnOrderList(
      filterData,
      page,
      perPage,
      sort
    );

    return res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.GET_PM_RETURN_ORDER, pmReturnOrder));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Wm Reject Missing Equipment
 *
 * @param {*} req
 * @param {*} res
 */
exports.wmRejectMissingEquipment = async (req, res) => {
  const session = await mongoose.startSession();
  const transactionOptions = { ...transactionOption };

  try {
    session.startTransaction(transactionOptions);
    const { returnOrder = null, equipment = null } = req.query;

    if (
      !returnOrder ||
      (mongoose.Types.ObjectId.isValid(returnOrder) && !equipment) ||
      !mongoose.Types.ObjectId.isValid(equipment)
    ) {
      await session.abortTransaction();
      session.endSession();

      return res.status(400).json(responseUtils.errorResponse(constantUtils.MISSING_FIELDS));
    }

    const returnOrderData = await returnOrderService.getSingleReturnOrderDataByFilter({
      _id: commonUtils.toObjectId(returnOrder),
      account: req.userData.account,
      deletedAt: null,
    });

    if (!returnOrderData) {
      await session.abortTransaction();
      session.endSession();

      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.RETURN_ORDER_NOT_FOUND));
    }

    const returnOrderHistoryData = await returnOrderHistoryService.getSingleReturnOrderHistory({
      returnOrder: commonUtils.toObjectId(returnOrder),
      equipment: commonUtils.toObjectId(equipment),
      account: req.userData.account,
      deletedAt: null,
    });

    if (!returnOrderHistoryData) {
      await session.abortTransaction();
      session.endSession();

      return res.status(400).json(responseUtils.errorResponse(constantUtils.FAILED_ITEMS));
    }

    const updateReturn = await returnOrderHistoryService.updateReturnOrder(
      {
        returnOrder: commonUtils.toObjectId(returnOrder),
        equipment: commonUtils.toObjectId(equipment),
        account: req.userData.account,
        deletedAt: null,
      },
      {
        status: 'rejected',
        wmComment: req.body.comment ? req.body.comment : '',
        wmReceivedQuantity: 0,
        pmDispatchedQuantity: req.body.pmDispatchedQuantity,
        updatedBy: req.userData._id,
      },
      session
    );

    if (!updateReturn) {
      await session.abortTransaction();
      session.endSession();

      return res.status(400).json(responseUtils.errorResponse(constantUtils.FAILED_ITEMS));
    }

    // update equipmentOrderHistory on reject
    const equipmentOrderHistoryFilter = {
      'returnOrder.returnOrder': commonUtils.toObjectId(returnOrder),
      equipment: commonUtils.toObjectId(equipment),
      account: req.userData.account,
      deletedAt: null,
    };

    const getEquipmentOrderHistory = await equipmentOrderHistoryService.getEquipmentHistory(
      equipmentOrderHistoryFilter
    );

    let updateEquipmentOrderHistory = {
      returnStatus: '',
      returnOrder: [],
      pmDispatchQuantity: null,
    };

    if (
      getEquipmentOrderHistory?.pmDispatchQuantity !== returnOrderHistoryData.pmDispatchQuantity
    ) {
      await equipmentOrderHistoryService.updateEquipmentForReturnOrder(
        getEquipmentOrderHistory._id,
        commonUtils.toObjectId(returnOrder),
        session
      );

      // update equipmentOrderHistoryFilter
      delete equipmentOrderHistoryFilter['returnOrder.returnOrder'];
      equipmentOrderHistoryFilter._id = getEquipmentOrderHistory._id;

      // update equipmentOrderHistory request
      delete updateEquipmentOrderHistory.returnOrder;
      updateEquipmentOrderHistory.returnStatus = 'partially-returned';
      updateEquipmentOrderHistory.pmDispatchQuantity =
        getEquipmentOrderHistory.pmDispatchQuantity - returnOrderHistoryData.pmDispatchQuantity;
    } else {
      const updatedReturnOrder = getEquipmentOrderHistory.returnOrder.filter(
        item => item.returnOrder.toString() !== returnOrderHistoryData.returnOrder.toString()
      );

      updateEquipmentOrderHistory.returnOrder = updatedReturnOrder;
    }

    const updateEquipHistory = await equipmentOrderHistoryService.updateEquipmentHistoryByFilter(
      equipmentOrderHistoryFilter,
      updateEquipmentOrderHistory,
      session
    );

    if (!updateEquipHistory) {
      await session.abortTransaction();
      session.endSession();

      return res.status(400).json(responseUtils.errorResponse(constantUtils.FAILED_ITEMS));
    }

    const inventoryHistoryUpdate = await inventoryHistoryService.prepareInventoryHistoryAndCreate(
      commonUtils.toObjectId(equipment),
      'on-site',
      'return-rejected',
      returnOrderHistoryData.project.title,
      returnOrderData.orderNumber,
      returnOrderHistoryData.pmDispatchQuantity,
      '',
      commonUtils.toObjectId(returnOrderData._id),
      req.userData.account,
      req.userData._id,
      session
    );

    if (!inventoryHistoryUpdate) {
      await session.abortTransaction();
      session.endSession();

      return res.status(400).json(responseUtils.errorResponse(constantUtils.FAILED_ITEMS));
    }

    await session.commitTransaction();
    session.endSession();

    session.startTransaction(transactionOptions);

    const anyNotRejected = await returnOrderHistoryService.getReturnOrderHistory({
      returnOrder: commonUtils.toObjectId(returnOrder),
      status: { $ne: 'rejected' },
      account: req.userData.account,
      deletedAt: null,
    });

    if (anyNotRejected.length === 0) {
      const updateOrder = await returnOrderService.updateReturn(
        {
          _id: commonUtils.toObjectId(returnOrder),
          account: req.userData.account,
          deletedAt: null,
        },
        {
          status: 'rejected',
          updatedBy: req.userData._id,
        }
      );

      if (!updateOrder) {
        await session.abortTransaction();
        session.endSession();

        return res.status(400).json(responseUtils.errorResponse(constantUtils.FAILED_ITEMS));
      }
    }

    await session.commitTransaction();
    session.endSession();

    res.status(200).json(responseUtils.successResponse(constantUtils.MISSING_EQUIPMENT_ADDED));
  } catch (error) {
    await session.abortTransaction();
    session.endSession();

    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Update Return Order
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.updateReturnOrder = async (req, res) => {
  try {
    const { returnOrder } = req.params;

    if (!mongoose.Types.ObjectId.isValid(returnOrder)) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.INVALID_RETURN_ORDER));
    }

    const exist = await returnOrderHistoryService.getReturnOrderHistoryByFilter({
      returnOrder: commonUtils.toObjectId(returnOrder),
      account: req.userData.account,
      deletedAt: null,
    });

    if (!exist) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.RETURN_ORDER_NOT_FOUND));
    }

    const updateReturn = await returnOrderHistoryService.updateReturnOrder(
      {
        returnOrder: commonUtils.toObjectId(returnOrder),
        account: req.userData.account,
        deletedAt: null,
      },
      {
        wmComment: req.body.wmComment ? req.body.wmComment : '',
      }
    );

    if (!updateReturn) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.FAILED_ITEMS));
    }

    res.status(200).json(responseUtils.successResponse(constantUtils.RETURN_ORDER_UPDATED));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.validateRequestData = async reqData => {
  if (reqData.length === 0) {
    return constantUtils.RETURN_CART_IS_EMPTY;
  }
  if (!reqData[0].projectId) {
    return constantUtils.PROJECT_ID_REQUIRED;
  }
  return null;
};

exports.updateEquipmentOrderHistory = async (
  equipmentHistoryIds,
  returnOrderId,
  userId,
  session
) => {
  for (const equipmentHistoryId of equipmentHistoryIds) {
    let equipmentOrder = await equipmentOrderHistoryService.getEquipmentOrderHistoryOneByFilter({
      _id: commonUtils.toObjectId(equipmentHistoryId),
    });

    let reqDat;
    if (equipmentOrder) {
      reqDat = {
        returnOrder: [...equipmentOrder.returnOrder, { returnOrder: returnOrderId, quantity: 0 }],
        returnStatus: 'pre-return',
        updatedBy: userId,
        updatedAt: new Date(),
      };
    } else {
      reqDat = {
        returnOrder: [{ returnOrder: returnOrderId, quantity: 0 }],
        returnStatus: 'pre-return',
        updatedBy: userId,
        updatedAt: new Date(),
      };
    }

    await equipmentOrderHistoryService.updateEquipmentOrderHistory(
      equipmentHistoryId,
      reqDat,
      session
    );
  }
};

exports.processEquipment = async (reqData, returnOrderId, userId, account, session) => {
  for (const data of reqData) {
    const { projectId, equipmentId, equipmentHistoryIds, pmReceivedQuantity, pmDispatchQuantity } =
      data;

    await this.updateEquipmentOrderHistory(equipmentHistoryIds, returnOrderId, userId, session);

    let existReturnEquipment = await returnOrderHistoryService.getSingleReturnOrderHistory({
      equipment: equipmentId,
      returnOrder: returnOrderId,
      status: 'pre-return',
      account: account,
      deletedAt: null,
    });

    if (!existReturnEquipment) {
      await returnOrderHistoryService.createReturnOrderHistory(
        {
          equipment: equipmentId,
          returnOrder: returnOrderId,
          status: 'pre-return',
          pmReceivedQuantity,
          pmDispatchQuantity,
          project: projectId,
          account: account,
          createdBy: userId,
          createdAt: new Date(),
        },
        session
      );
    } else {
      await returnOrderHistoryService.updateReturnOrderHistory(
        existReturnEquipment._id,
        {
          pmReceivedQuantity: pmReceivedQuantity + existReturnEquipment.pmReceivedQuantity,
          updatedBy: userId,
          updatedAt: new Date(),
        },
        session
      );
    }
  }
};

/**
 * get project return order list
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getProjectReturnOrderList = async (req, res) => {
  try {
    let page = req.query.page ? req.query.page : 0;
    let perPage = req.query.perPage ? req.query.perPage : 100;
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;
    let project = req.query.project ? commonUtils.toObjectId(req.query.project) : null;
    let search = req.query.search;

    let filterData = {
      ...(project !== null && { project }),
      status: req.query.status ?? { $in: ['return', 'rejected'] },
      account: req.userData.account,
      deletedAt: null,
    };

    // Add project filter if project status is provided
    if ('projectStatus' in req.query) {
      filterData = await commonUtils.filterProjectStatus(
        req.query.projectStatus,
        req.userData.account,
        filterData
      );
    }
    delete filterData?.projectStatus;
    const pmReturnOrder = await returnOrderService.getProjectReturnOrderList(
      filterData,
      page,
      perPage,
      sort,
      search
    );
    return res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.GET_PM_RETURN_ORDER, pmReturnOrder));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * get return order list by project
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getReturnOrderListByProject = async (req, res) => {
  try {
    let page = req.query.page ? req.query.page : 0;
    let perPage = req.query.perPage ? req.query.perPage : 100;
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;
    let search = req.query.search;
    let filterData = {
      project: commonUtils.toObjectId(req.params.id),
      status: req.query.status ?? { $in: ['return', 'rejected'] },
      account: req.userData.account,
      deletedAt: null,
    };

    delete filterData?.projectStatus;
    const [pmReturnOrder] = await returnOrderService.pmReturnOrderList(
      filterData,
      search,
      page,
      perPage,
      sort
    );

    return res
      .status(HTTP_STATUS.OK)
      .json(responseUtils.successResponse(constantUtils.GET_PM_RETURN_ORDER, pmReturnOrder));
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};
