const dateFormat = require('dateformat');

// Utils
const responseUtils = require('../utils/response.utils');
const constantUtils = require('../utils/constants.utils');
const exportExcelUtils = require('../utils/export-excel.util');
const commonUtils = require('../utils/common.utils');
const exportPDFUtils = require('../utils/export-pdf.utils');
const HTTP_STATUS = require('../utils/status-codes');

// Static Fields
const staticFields = require('../utils/static-fields.utils');

// Dynamic Fields
const dynamicFields = require('../utils/dynamic-fields.utils');

// Services
const safetyCardService = require('../services/safety-card.service');
const projectService = require('../services/project.service');
const pdfTemplateService = require('../services/pdf-template.service');
const toolboxTalkService = require('../services/toolbox-talk.service');
const dprService = require('../services/dpr.service');

/**
 * Create a New Safety Card
 *
 * @param {*} req
 * @param {*} res
 */
exports.createSafetyCard = async (req, res) => {
  try {
    let reqData = req.body;
    const project = await projectService.getProjectById(reqData.project, req.userData.account);

    // If default project is selected
    if (project.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
      reqData.defaultProject = await safetyCardService.getDefaultProject(req);
      reqData.isDefault = true;
    } else {
      reqData.defaultProject = null;
      reqData.isDefault = false;
    }
    // End of if default project is selected

    if (!commonUtils.isValidId(reqData.location)) {
      reqData.location = await safetyCardService.getlocation(req);
      reqData.isDefault = true;
    }
    reqData.createdBy = req.userData._id;
    reqData.updatedBy = req.userData._id;
    reqData = await safetyCardService.prepareCardLogs(reqData, 'create', req.userData._id);
    reqData = await safetyCardService.changeSafetyCard(req, reqData);

    const createSafetyCard = await safetyCardService.create(reqData);

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.CREATE_SAFETY_CARD, createSafetyCard));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get Safety Card form fields by id
 *
 * @param {*} req
 * @param {*} res
 */
exports.getSafetyCardform = async (req, res) => {
  try {
    const id = req.params.id;
    const cardType = req.params.cardtype;
    const safetyCard = await safetyCardService.getSafetyCardById(id);

    let ids = safetyCard.dynamicFields.map(item => item.fieldId);

    let hseStaticFields = null;

    let tokenData = {
      account: req.userData.account,
      role: req.userData.role.title,
      id: req.userData._id,
    };

    if (cardType === 'safe') {
      // Safe card Static fields
      hseStaticFields = await staticFields.GetSafeCardStaticFile(tokenData, false);
      await this.updateDefaultFields(hseStaticFields, safetyCard.project.defaultIdentifier);
    } else if (cardType === 'unsafe') {
      // Unsafe card Static fields
      hseStaticFields = await staticFields.GetUnSafeCardStaticFile(tokenData, false);
      await this.updateDefaultFields(hseStaticFields, safetyCard.project.defaultIdentifier);
    } else if (cardType === 'ncr') {
      // NCR card Static fields
      hseStaticFields = await staticFields.GetNCRCardStaticFile(tokenData, false);
      await this.updateDefaultFields(hseStaticFields, safetyCard.project.defaultIdentifier);
    } else {
      // Incident card Static fields
      hseStaticFields = await staticFields.GetIncidentCardStaticFile(tokenData, false);
      await this.updateDefaultFields(hseStaticFields, safetyCard.project.defaultIdentifier);
    }

    // Safety card dynamic fields
    let safetyCardDynamicFields = await dynamicFields.GetSafetyCardDynamicFeildsByIds(ids);

    if (!safetyCard) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_SAFETY_CARD));
    }

    res
      .status(200)
      .json(
        responseUtils.successResponse(constantUtils.SAFETY_CARD_UPDATE_FORM, [
          ...hseStaticFields,
          ...safetyCardDynamicFields,
        ])
      );
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.updateDefaultFields = async (fields, projectIdentifier) => {
  if (projectIdentifier === global.constant.DEFAULT_DATA_IDENTIFIER) {
    fields.forEach(item => {
      if (item.title === process.env.DEFAULT_PROJECT.replace(/_/g, ' ')) {
        item.isDefaultVisible = true;
        item.IsRequired = true;
      }
    });
  }
};

/**
 * Update Safety Card
 *
 * @param {*} req
 * @param {*} res
 */
exports.updateSafetyCard = async (req, res) => {
  try {
    const id = req.params.id;
    const safetyCard = await safetyCardService.getSafetyCardById(id);
    if (!safetyCard) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_SAFETY_CARD));
    }

    const project = await projectService.getProjectById(req.body.project, req.userData.account);

    // If default project is selected
    if (project.title == process.env.DEFAULT_PROJECT.replace(/_/g, ' ')) {
      req.body.defaultProject = await safetyCardService.getDefaultProject(req);
      req.body.isDefault = true;
    } else {
      req.body.defaultProject = null;
      req.body.isDefault = false;
    }
    // End of if default project is selected

    if ('severity' in req.body && 'likelihood' in req.body) {
      const result = await safetyCardService.calculateRiskFactor(req);
      req.body.riskFactor = result;
    }
    let newReq = { ...req.body, updatedBy: req.userData.id, updatedAt: new Date() };
    if ('location' in req.body) {
      if (!commonUtils.isValidId(req.body.location)) {
        newReq.location = await safetyCardService.getlocation(req);
        newReq.isDefault = true;
      }
    }
    const data = await safetyCardService.update(id, newReq);

    if (data) {
      await safetyCardService.prepareCardLogs(data, 'update', req.userData._id, id);
    }

    res.status(200).json(responseUtils.successResponse(constantUtils.SAFETY_CARD_UPDATE, data));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get Safety Card by id
 *
 * @param {*} req
 * @param {*} res
 */
exports.getSafetyCardById = async (req, res) => {
  try {
    const id = req.params.id;
    const safetyCard = await safetyCardService.getSafetyCardById(id);

    if (!safetyCard) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_SAFETY_CARD));
    }
    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.SAFETY_CARD_BY_ID, safetyCard));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get all Safety Card
 *
 * @param {*} req
 * @param {*} res
 */
exports.getAllSafetyCard = async (req, res) => {
  try {
    let page = req.query.page ? req.query.page : 0;
    let perPage = req.query.perPage ? req.query.perPage : 100;
    let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;
    let queryProject = req.query.project;

    let filterData = req.query;
    let removeKeys = ['page', 'perPage', 'sort'];
    filterData = await commonUtils.filterParamsModify(filterData, removeKeys);
    filterData = await commonUtils.getCreatedDateFilter(filterData);
    filterData.account = req.userData.account;
    filterData.deletedAt = null;

    if (filterData.project == 'otherProject' || filterData.location == 'otherLocation') {
      filterData.isDefault = true;
      if (
        !commonUtils.isValidId(filterData.project) ||
        !commonUtils.isValidId(filterData.location)
      ) {
        delete filterData.project;
        delete filterData.location;
      }
    }

    // Add project filter if project status is provided
    if ('projectStatus' in req.query && queryProject === 'all') {
      filterData = await commonUtils.filterProjectStatus(
        req.query.projectStatus,
        req.userData.account,
        filterData
      );
    }

    delete filterData?.projectStatus;

    if (
      ![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(
        req.userData.role.title
      ) &&
      queryProject === 'all'
    ) {
      let searchData = {
        ...(req.userData.role.isAssignAllProjects ? {} : { user: req.userData._id }),
        account: req.userData.account,
        deletedAt: null,
      };

      const projectList = await commonUtils.getAssignedProjectList(
        req.userData.role.isAssignAllProjects,
        searchData
      );

      if (projectList.length > 0) {
        filterData.project = { $in: projectList };
      }
    }

    if (req?.query?.severity) {
      const severityValues = req.query.severity.split(',');
      if (req?.query?.cardType) {
        if (req.query.cardType.includes('safe') || req?.query?.cardType.includes('ncr')) {
          severityValues.push(null);
        }
      }

      filterData.severity = {
        $in: severityValues.map(val => (val === 'null' ? null : val)),
      };
    }

    if (req?.query?.likelihood) {
      const likelihoodValues = req.query.likelihood.split(',');
      if (req?.query?.cardType) {
        if (req.query.cardType.includes('safe') || req?.query?.cardType.includes('ncr')) {
          likelihoodValues.push(null);
        }
      }

      filterData.likelihood = {
        $in: likelihoodValues.map(val => (val === 'null' ? null : val)),
      };
    }

    /** Category Filter */

    if (req?.query?.category) {
      filterData.category = commonUtils.toObjectId(req.query.category);
    }
    /** Category Filter */

    if (req?.query?.cardType) {
      filterData.cardType = { $in: req.query.cardType.split(',') };
    }

    if (req?.query?.status) {
      filterData.status = { $in: req.query.status.split(',') };
      if (filterData.status.$in.includes('submitted')) {
        filterData.status.$in.push('submitted(client)');
      }
    }

    const data = await safetyCardService.getAllSafetyCard(page, perPage, filterData, sort);

    // add all records count
    let finalResponse = {
      qhseData: data,
      currentPage: Number(page),
    };
    finalResponse = await commonUtils.getCountFromQuery('safety-card', filterData, finalResponse);

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.ALL_SAFETY_CARD_LIST, finalResponse));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Delete Safety Card
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.deleteSafetyCard = async (req, res) => {
  try {
    const id = req.params.id;
    const safetyCard = await safetyCardService.getSafetyCardById(id);

    if (!safetyCard) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.NO_SAFETY_CARD));
    }
    await safetyCardService.deleteSafetyCard(id, req.deletedAt);

    res.status(200).json(responseUtils.successResponse(constantUtils.DELETED_SAFETY_CARD));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * export safety cards of login users
 *
 * @param {*} req
 * @param {*} res
 */
exports.exportSafetyCards = async (req, res) => {
  try {
    const user = req.userData;
    let requestParams = req.query ?? {};
    let page = requestParams.page ? requestParams.page : '';
    let perPage = requestParams.perPage ? requestParams.perPage : '';
    let sort = requestParams.sort && requestParams.sort === 'asc' ? 1 : -1;
    const nowDate = dateFormat(new Date(), global.constant.DATE_FORMAT_FOR_EXPORT);
    let fileName = `QHSECard-${nowDate}`;
    let requestFormat = req.query.format ?? 'excel';
    requestFormat = requestFormat.toLowerCase();
    let queryProject = req.query.project;

    if (!global.constant.EXPORT_SERVICES.includes(requestFormat)) {
      return res
        .status(400)
        .json(responseUtils.errorResponse(constantUtils.FILE_FORMAT_NOT_EXISTS));
    }

    requestParams = await exports.modifyRequestParamter(requestParams, user.account);

    // Add project filter if project status is provided
    if ('projectStatus' in req.query && queryProject === 'all') {
      requestParams = await commonUtils.filterProjectStatus(
        req.query.projectStatus,
        req.userData.account,
        requestParams
      );
    }

    delete requestParams?.projectStatus;

    if (
      ![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(
        req.userData.role.title
      ) &&
      queryProject == 'all'
    ) {
      let searchData = {
        ...(req.userData.role.isAssignAllProjects ? {} : { user: req.userData._id }),
        account: req.userData.account,
        deletedAt: null,
      };

      const projectList = await commonUtils.getAssignedProjectList(
        req.userData.role.isAssignAllProjects,
        searchData
      );

      if (projectList.length > 0) {
        requestParams.project = { $in: projectList };
      }
    }

    if (req?.query?.severity) {
      const severityValues = req.query.severity.split(',');
      if (req?.query?.cardType) {
        if (req.query.cardType.includes('safe') || req?.query?.cardType.includes('ncr')) {
          severityValues.push(null);
        }
      }

      requestParams.severity = {
        $in: severityValues.map(val => (val === 'null' ? null : val)),
      };
    }

    if (req?.query?.likelihood) {
      const likelihoodValues = req.query.likelihood.split(',');
      if (req?.query?.cardType) {
        if (req.query.cardType.includes('safe') || req?.query?.cardType.includes('ncr')) {
          likelihoodValues.push(null);
        }
      }

      requestParams.likelihood = {
        $in: likelihoodValues.map(val => (val === 'null' ? null : val)),
      };
    }

    if (req?.query?.cardType) {
      requestParams.cardType = { $in: req.query.cardType.split(',') };
    }

    if (req?.query?.status) {
      requestParams.status = { $in: req.query.status.split(',') };
    }

    const safetyCards = await safetyCardService.getAllSafetyCard(
      page,
      perPage,
      requestParams,
      sort
    );

    if (safetyCards.length === 0) {
      return res.status(404).json(responseUtils.errorResponse(constantUtils.NO_SAFETY_CARD));
    }

    const tableData = await safetyCardService.getTableData(safetyCards, user, requestFormat);

    return tableData.type === 'excel'
      ? await exportExcelUtils.exportExcel(res, fileName, tableData.columns, tableData.rows)
      : await exportPDFUtils.exportPDFTable(
          res,
          fileName,
          tableData.columns,
          tableData.rows,
          'QHSE Cards',
          20,
          tableData.columnSize
        );
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

exports.modifyRequestParamter = async (requestParams, account) => {
  let removeKeys = ['page', 'perPage', 'sort'];
  requestParams = await commonUtils.filterParamsModify(requestParams, removeKeys);
  requestParams.account = account;
  requestParams = await commonUtils.getCreatedDateFilter(requestParams);
  requestParams.deletedAt = null;

  // If default project or location is selected
  if (requestParams.project == 'otherProject' || requestParams.location == 'otherLocation') {
    requestParams.isDefault = true;
    if (
      !commonUtils.isValidId(requestParams.project) ||
      !commonUtils.isValidId(requestParams.location)
    ) {
      delete requestParams.project;
      delete requestParams.location;
    }
  }

  return requestParams;
};

/**
 * QHSC Card Count
 *
 * @param {*} req
 * @param {*} res
 */
exports.qhscCardCount = async (req, res) => {
  try {
    let project =
      req.query?.project && req.query?.project !== 'all'
        ? commonUtils.toObjectId(req.query.project)
        : null;

    let queryProject = req.query.project;
    let created =
      req.query?.created && req.query?.created !== 'all'
        ? await commonUtils.getCreatedDateFilter({
            created: req.query.created,
            fromDate: req.query.fromDate,
            toDate: req.query.toDate,
          })
        : null;

    let filter = {
      account: req.userData.account,
      ...(project !== null && { project }),
      ...(created !== null && {
        createdAt: created.createdAt,
      }),
      deletedAt: null,
    };

    // Add project filter if project status is provided
    if ('projectStatus' in req.query && req.query?.project === 'all') {
      filter = await commonUtils.filterProjectStatus(
        req.query.projectStatus,
        req.userData.account,
        filter
      );
    }

    delete filter?.projectStatus;

    filter = await commonUtils.applyProjectFilterForNonAdminUsers(
      filter,
      req.userData,
      queryProject
    );

    const response = await safetyCardService.qhscCardCount(filter);

    res.status(200).json(responseUtils.successResponse(constantUtils.SAFETY_CARD_COUNT, response));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * QHSC Card Risk Of Incident Count
 *
 * @param {*} req
 * @param {*} res
 */
exports.qhscCardRiskOfIncidentCount = async (req, res) => {
  try {
    if (!commonUtils.isValidId(req.query.project) && commonUtils.isValidId(req.query.created)) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.INVALID_ID));
    }

    let project =
      req.query?.project && req.query?.project !== 'all'
        ? commonUtils.toObjectId(req.query.project)
        : null;

    let queryProject = req.query.project;
    if (project) {
      const projectExist = await projectService.getProjectById(project, req.userData.account);
      if (!projectExist) {
        return res
          .status(HTTP_STATUS.BAD_REQUEST)
          .json(responseUtils.errorResponse(constantUtils.NO_PROJECT));
      }
    }

    let created =
      req.query?.created && req.query?.created !== 'all'
        ? await commonUtils.getCreatedDateFilter({
            created: req.query.created,
            fromDate: req.query.fromDate,
            toDate: req.query.toDate,
          })
        : null;

    let filter = {
      account: req.userData.account,
      ...(project !== null && { project }),
      ...(created !== null && {
        createdAt: created.createdAt,
      }),
      riskFactor: { $ne: null },
      deletedAt: null,
      cardType: { $in: ['incident', 'unsafe'] },
    };

    if ('projectStatus' in req.query && queryProject === 'all') {
      filter = await commonUtils.filterProjectStatus(
        req.query.projectStatus,
        req.userData.account,
        filter
      );
    }

    delete filter?.projectStatus;

    filter = await commonUtils.applyProjectFilterForNonAdminUsers(
      filter,
      req.userData,
      queryProject
    );

    const response = await safetyCardService.qhscCardRiskOfIncidentCount(filter);

    res
      .status(HTTP_STATUS.OK)
      .json(
        responseUtils.successResponse(constantUtils.SAFETY_CARD_RISK_OF_INCIDENT_COUNT, response)
      );
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * QHSC Card Risk Of Incident Count
 *
 * @param {*} req
 * @param {*} res
 */
exports.qhscCardTypeOfIncidentCount = async (req, res) => {
  try {
    if (!commonUtils.isValidId(req.query.project) && commonUtils.isValidId(req.query.created)) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.INVALID_ID));
    }

    let queryProject = req.query.project;

    let project =
      req.query?.project && req.query?.project !== 'all'
        ? commonUtils.toObjectId(req.query.project)
        : null;

    if (project) {
      const projectExist = await projectService.getProjectById(project, req.userData.account);
      if (!projectExist) {
        return res
          .status(HTTP_STATUS.BAD_REQUEST)
          .json(responseUtils.errorResponse(constantUtils.NO_PROJECT));
      }
    }

    let created =
      req.query?.created && req.query?.created !== 'all'
        ? await commonUtils.getCreatedDateFilter({
            created: req.query.created,
            fromDate: req.query.fromDate,
            toDate: req.query.toDate,
          })
        : null;

    let filter = {
      account: req.userData.account,
      ...(project !== null && { project }),
      ...(created !== null && {
        createdAt: created.createdAt,
      }),
      deletedAt: null,
    };

    // Add project filter if project status is provided
    if ('projectStatus' in req.query && queryProject === 'all') {
      filter = await commonUtils.filterProjectStatus(
        req.query.projectStatus,
        req.userData.account,
        filter
      );
    }

    delete filter?.projectStatus;

    filter = await commonUtils.applyProjectFilterForNonAdminUsers(
      filter,
      req.userData,
      queryProject
    );

    const response = await safetyCardService.qhscCardTypeOfIncidentCount(filter);

    res
      .status(HTTP_STATUS.OK)
      .json(
        responseUtils.successResponse(constantUtils.SAFETY_CARD_RISK_OF_INCIDENT_COUNT, response)
      );
  } catch (error) {
    res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Get Safety Card PDF Details
 *
 * @param {*} req
 * @param {*} res
 * @returns
 */
exports.getSafetyCardPDFDetails = async (req, res) => {
  try {
    const { safetyCardId } = req.params;

    const response = await safetyCardService.getSafetyCardById(safetyCardId);

    if (response.length === 0) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.MISSING_ORDER_DATA));
    }

    response.userTimezone = req.userTimezone;
    return await pdfTemplateService.exportSafetyCardPDF(response, res);
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Safety Cards Summary
 *
 * @param {*} req
 * @param {*} res
 */
exports.safetyCardsSummary = async (req, res) => {
  try {
    const { dprId } = req.params;
    const dpr = await dprService.getLatestDpr({
      _id: dprId,
      account: req.userData.account,
      deletedAt: null,
    });
    if (!dpr) {
      return res
        .status(HTTP_STATUS.BAD_REQUEST)
        .json(responseUtils.errorResponse(constantUtils.NO_DPR));
    }
    let filter = {
      project: dpr.project,
      account: req.userData.account,
      deletedAt: null,
    };
    const response = await safetyCardService.safetyCardsSummary({
      ...filter,
      status: { $in: ['submitted', 'submitted(client)', 'in_discussion', 'closed'] },
    });
    const toolboxTalk = await toolboxTalkService.getToolboxTalkSummary(filter);
    const result = await safetyCardService.safetyCardsSummaryCount(
      response,
      toolboxTalk,
      dpr.dprDate,
      req.userTimezone
    );
    res.status(200).json(responseUtils.successResponse(constantUtils.SAFETY_CARD_COUNT, result));
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};
