const CertificateType = require('../models/certificate-type.model');

//Utils
const commonfunctionUtils = require('../utils/common-function.utils');

/**
 * Create CertificateType
 *
 * @param {*} requestData
 * @returns
 */
exports.createCertificateType = async requestData => {
  return await CertificateType.create(requestData);
};

/**
 * GetByName CertificateType
 *
 * @param {*} name
 * @param {*} account
 * @returns
 */
exports.getByNameCertificateType = async (name, account) => {
  return await CertificateType.find({
    $and: [{ name: name }, { account: account }, { deletedAt: null }],
  }).populate(commonfunctionUtils.certificateType);
};

/**
 * Get CertificateType
 *
 * @param {*} filter
 * @param {*} perPage
 * @param {*} page
 * @param {*} sort
 * @returns
 */

exports.getCertificateType = async (filter, page, perPage, sort = -1) => {
  return await CertificateType.find(filter, {
    deletedAt: 0,
    deletedBy: 0,
    __v: 0,
  })
    .populate([
      {
        path: 'account',
        select: { _id: 1, name: 1 },
        options: { strictPopulate: false },
      },
      {
        path: 'createdBy',
        model: 'user',
        select: { callingName: 1, firstName: 1, lastName: 1, _id: 1 },
        populate: {
          path: 'role',
          select: { title: 1 },
        },
        options: { strictPopulate: false },
      },
      {
        path: 'updatedBy',
        model: 'user',
        select: { callingName: 1, firstName: 1, lastName: 1, _id: 1 },
        populate: {
          path: 'role',
          select: { title: 1 },
        },
        options: { strictPopulate: false },
      },
      {
        path: 'deletedBy',
        model: 'user',
        select: { callingName: 1, firstName: 1, lastName: 1, _id: 1 },
        populate: {
          path: 'role',
          select: { title: 1 },
        },
        options: { strictPopulate: false },
      },
    ])
    .sort({ sortOrder: sort })
    .skip(page * perPage)
    .limit(perPage);
};

/**
 * Get CertificateType by Id
 *
 * @param {*} id
 * @returns
 */
exports.getCertificateTypeById = async id => {
  return await CertificateType.findOne({
    _id: id,
    deletedAt: null,
  }).populate(commonfunctionUtils.certificateType);
};

/**
 * Update certificateType
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */

exports.updateCertificateType = async (id, requestData) => {
  return await CertificateType.findByIdAndUpdate(id, { $set: requestData }, { new: true })
    .populate({
      path: 'user createdBy',
      select: { callingName: 1, firstName: 1, lastName: 1, _id: 1 },
      strictPopulate: false,
    })
    .populate({
      path: 'user updatedBy',
      select: { callingName: 1, firstName: 1, lastName: 1, _id: 1 },
      populate: {
        path: 'role',
        select: { title: 1 },
      },
      strictPopulate: false,
    })
    .populate({
      path: 'user deletedBy',
      select: { callingName: 1, firstName: 1, lastName: 1, _id: 1 },
      strictPopulate: false,
    });
};
/**
 * Delete certificateType
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteCertificateType = async (id, deletedAt) => {
  return await CertificateType.findByIdAndUpdate(id, { $set: deletedAt }, { new: true });
};

exports.filterSingleCertificateType = async filter => {
  return await CertificateType.findOne(filter);
};

/**
 * Get certificate list
 *
 * @param {*} filter
 * @returns
 */
exports.getUserCertificatesList = async filter => {
  let aggregateFilter = [
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'certificates',
        localField: '_id',
        foreignField: 'certificates',
        pipeline: [
          {
            $project: {
              project: 1,
            },
          },
          {
            $lookup: {
              from: 'projects',
              localField: 'project',
              foreignField: '_id',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    title: 1,
                  },
                },
              ],
              as: 'project',
            },
          },
          { $unwind: '$project' },
        ],
        as: 'certificate',
      },
    },
    {
      $unwind: { path: '$certificate', preserveNullAndEmptyArrays: true },
    },
    {
      $group: {
        _id: '$_id',
        name: { $first: '$name' },
        validityDate: { $first: '$validityDate' },
        project: { $push: '$certificate.project.title' },
      },
    },
    {
      $addFields: {
        // Remove duplicates from the 'project' array
        project: { $setUnion: ['$project', []] },
      },
    },
    {
      $addFields: {
        projectCertificate: {
          $concat: [
            { $ifNull: ['$name', ''] },
            {
              $cond: [
                { $and: [{ $isArray: '$project' }, { $gt: [{ $size: '$project' }, 0] }] },
                {
                  $concat: [
                    ' (',
                    {
                      $reduce: {
                        input: '$project',
                        initialValue: '',
                        in: {
                          $concat: [
                            '$$value',
                            { $cond: [{ $ne: ['$$value', ''] }, ', ', ''] },
                            '$$this',
                          ],
                        },
                      },
                    },
                    ')',
                  ],
                },
                '',
              ],
            },
          ],
        },
        hasProjects: {
          $cond: [
            { $and: [{ $isArray: '$project' }, { $gt: [{ $size: '$project' }, 0] }] },
            1, // Has projects
            0, // No projects
          ],
        },
      },
    },
    {
      $sort: {
        hasProjects: -1, // Projects first
        sortOrder: 1, // Then by sortOrder
      },
    },
  ];

  return CertificateType.aggregate(aggregateFilter);
};
