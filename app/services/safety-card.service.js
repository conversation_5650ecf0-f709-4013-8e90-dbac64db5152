const safetyCardModel = require('../models/safety-card.model');
const Type = require('../models/type.model');
const severityService = require('../services/severity.service');
const likelihoodService = require('../services/likelihood.service');
const locationService = require('../services/location.service');
const projectService = require('../services/project.service');

const commonUtils = require('../utils/common.utils');
const { safetyCardOrder, safetyCardStatusOrder } = require('../utils/json-format.utils');

/**
 * Create New Safety Card
 *
 * @param {*} data
 * @returns
 */
exports.create = async data => {
  return await safetyCardModel.create(data);
};

/**
 * Update Safety Card
 *
 * @param {*} id
 * @param {*} data
 * @returns
 */
exports.update = async (id, data) => {
  return await safetyCardModel.findByIdAndUpdate(id, { $set: data }, { new: true }).populate([
    {
      path: 'project',
      select: { title: 1, _id: 1 },
      strictPopulate: false,
    },
    {
      path: 'location',
      select: { title: 1, _id: 1 },
      strictPopulate: false,
    },
    {
      path: 'defaultProject',
      select: { title: 1, _id: 1 },
      strictPopulate: false,
    },
    {
      path: 'category',
      select: { categoryName: 1, _id: 1, isVisibleForSafeCard: 1, isVisibleForUnsafeCard: 1 },
      strictPopulate: false,
    },
    {
      path: 'user createdBy',
      select: { callingName: 1, firstName: 1, lastName: 1, _id: 1 },
      strictPopulate: false,
    },
    {
      path: 'user updatedBy',
      select: { callingName: 1, firstName: 1, lastName: 1, _id: 1 },
      strictPopulate: false,
    },
    {
      path: 'user deletedBy',
      select: { callingName: 1, firstName: 1, lastName: 1, _id: 1 },
      strictPopulate: false,
    },
    {
      path: 'action',
      select: { title: 1, _id: 1 },
      strictPopulate: false,
    },
    {
      path: 'severity',
      select: { title: 1, _id: 1 },
      strictPopulate: false,
    },
    {
      path: 'likelihood',
      select: { title: 1, _id: 1 },
      strictPopulate: false,
    },
    {
      path: 'type',
      select: { title: 1, _id: 1 },
      strictPopulate: false,
    },
  ]);
};

/**
 * Get Safety Card By Id
 *
 * @param {*} name
 * @returns
 */
exports.getSafetyCardById = async id => {
  return await safetyCardModel
    .findOne({ _id: id, deletedAt: null }, { updatedAt: 0, __v: 0 })
    .populate([
      {
        path: 'project',
        select: { title: 1, defaultIdentifier: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'dynamicFields.fieldId',
        select: { fieldName: 1, fieldType: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'defaultProject',
        select: { title: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'location',
        select: { title: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'category',
        select: { categoryName: 1, _id: 1, isVisibleForSafeCard: 1, isVisibleForUnsafeCard: 1 },
        strictPopulate: false,
      },
      {
        path: 'user createdBy',
        select: { callingName: 1, firstName: 1, lastName: 1, _id: 1 },
        populate: {
          path: 'role',
          select: { title: 1 },
        },
        strictPopulate: false,
      },
      {
        path: 'user updatedBy',
        select: { callingName: 1, firstName: 1, lastName: 1, _id: 1 },
        populate: {
          path: 'role',
          select: { title: 1 },
        },
        strictPopulate: false,
      },
      {
        path: 'user deletedBy',
        select: { callingName: 1, firstName: 1, lastName: 1, _id: 1 },
        populate: {
          path: 'role',
          select: { title: 1 },
        },
        strictPopulate: false,
      },
      {
        path: 'severity',
        select: { title: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'likelihood',
        select: { title: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'type',
        select: { title: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'cardLogs.user',
        select: { _id: 1, callingName: 1, firstName: 1, lastName: 1 },
        strictPopulate: false,
      },
    ]);
};

/**
 * Get All Safety Cards
 *
 * @param {*} page
 * @param {*} perPage
 * @returns
 */
exports.getAllSafetyCard = async (page, perPage, filter, sort) => {
  let safetyCard = safetyCardModel
    .find(filter, { __v: 0 })
    .sort({ createdAt: sort ?? -1 })
    .populate([
      {
        path: 'project',
        select: { title: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'defaultProject',
        select: { title: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'location',
        select: { title: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'category',
        select: { categoryName: 1, _id: 1, isVisibleForSafeCard: 1, isVisibleForUnsafeCard: 1 },
        strictPopulate: false,
      },
      {
        path: 'user createdBy',
        select: { callingName: 1, firstName: 1, lastName: 1, _id: 1, role: 1 },
        populate: {
          path: 'role',
          select: { title: 1 },
        },
        strictPopulate: false,
      },
      {
        path: 'user updatedBy',
        select: { callingName: 1, firstName: 1, lastName: 1, _id: 1 },
        populate: {
          path: 'role',
          select: { title: 1 },
        },
        strictPopulate: false,
      },
      {
        path: 'user deletedBy',
        select: { callingName: 1, firstName: 1, lastName: 1, _id: 1 },
        populate: {
          path: 'role',
          select: { title: 1 },
        },
        strictPopulate: false,
      },
      {
        path: 'severity',
        select: { title: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'likelihood',
        select: { title: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'type',
        select: { title: 1, _id: 1 },
        strictPopulate: false,
      },
    ]);

  if (page === '' && perPage === '') {
    const result = await safetyCard.lean();

    const newSafetyCard = result.map(card => ({
      ...card,
      status: card.status === 'submitted' ? 'submitted(client)' : card.status,
    }));

    return newSafetyCard;
  }

  safetyCard = safetyCard.limit(perPage).skip(page * perPage);
  const paginatedSafetyCard = await safetyCard.lean();

  const alterSafetyCard = paginatedSafetyCard.map(card => ({
    ...card,
    status: card.status === 'submitted' ? 'submitted(client)' : card.status,
  }));

  return alterSafetyCard;
};

/**
 * Delete Safety card
 *
 * @param {*} id
 * @returns
 */
exports.deleteSafetyCard = async (id, deletedAt) => {
  return await safetyCardModel.findByIdAndUpdate(id, { $set: deletedAt });
};

/**
 * get safety cards by user id
 *
 * @param {*} userId
 * @returns
 */
exports.getSafetyCardByAccount = async filter => {
  return safetyCardModel
    .find(filter)
    .select(['-images', '-createdAt', '-updatedAt', '-__v'])
    .populate([
      {
        path: 'project',
        select: { title: 1 },
      },
      {
        path: 'severity',
        select: { title: 1 },
      },
      {
        path: 'likelihood',
        select: { title: 1 },
      },
      {
        path: 'location',
        select: { title: 1 },
      },
    ]);
};

/**
 * Get filter data
 *
 * @param {*} filter
 * @param {*} page
 * @param {*} perPage
 * @returns
 */
exports.getFilterData = async (filter, page, perPage) => {
  return safetyCardModel
    .find(filter, { __v: 0, createdBy: 0, createdAt: 0, updatedAt: 0 })
    .limit(perPage)
    .skip(page * perPage)
    .populate([
      {
        path: 'project',
        select: { title: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'user createdBy',
        select: { callingName: 1, firstName: 1, lastName: 1, _id: 1 },
        strictPopulate: false,
      },
    ]);
};

/**
 * Get Table Data
 *
 * @param {*} safetyCards
 * @param {*} user
 * @param {*} requestFormat
 * @returns
 */
exports.getTableData = async (safetyCards, user, requestFormat) => {
  let counter = 1;
  let tableData = {};
  let defaultTableHeaders = [
    { header: 'No.', key: 'srNo', width: 5 },
    { header: 'Project', key: 'project', width: 30 },
    { header: 'Type', key: 'cardType', width: 10 },
    { header: 'Title', key: 'title', width: 15 },
    { header: 'Location', key: 'location', width: 30 },
    { header: 'Category', key: 'category', width: 15 },
    { header: 'Risk Factor', key: 'riskFactor', width: 10 },
    { header: 'Status', key: 'status', width: 15 },
    { header: 'Created By', key: 'createdBy', width: 20 },
  ];
  let defaultRowData = [];
  Object.keys(safetyCards).forEach(key => {
    let cardType = '';
    if (safetyCards[key].cardType !== '') {
      cardType =
        safetyCards[key].cardType === 'ncr'
          ? safetyCards[key].cardType.toUpperCase()
          : safetyCards[key].cardType.charAt(0).toUpperCase() + safetyCards[key].cardType.slice(1);
    }

    // if default project not null then show other project
    let projectTitle = '';
    if (safetyCards[key].defaultProject !== null) {
      projectTitle = `${safetyCards[key].defaultProject.title} (Other Project)`;
    } else {
      projectTitle = safetyCards[key].project.title;
    }

    defaultRowData.push({
      srNo: counter,
      project: projectTitle,
      cardType,
      title: safetyCards[key].title ?? '',
      location: safetyCards[key].location != null ? safetyCards[key].location.title : '',
      category: safetyCards[key].category != null ? safetyCards[key].category.categoryName : '',
      riskFactor: safetyCards[key].riskFactor ?? '',
      status:
        safetyCards[key]?.status === 'submitted(client)'
          ? 'Submitted(Client)'
          : commonUtils.alterStringFromRequestString(safetyCards[key].status),
      createdBy:
        safetyCards[key].createdBy !== ''
          ? `${
              safetyCards[key].createdBy?.callingName
                ? safetyCards[key].createdBy?.callingName
                : safetyCards[key].createdBy.firstName
            } ${safetyCards[key].createdBy.lastName}`
          : '',
    });
    counter++;
  });
  switch (requestFormat) {
    case 'excel': {
      // table headers for excel
      tableData.columns = [...defaultTableHeaders];
      // table rows for excel
      tableData.rows = [...defaultRowData];
      break;
    }
    case 'pdf': {
      let headerColor = global.constant.PDF_HEADER_COLOR;
      let headerOpacity = 1;
      let valign = 'center';
      let pdfHeader = [];
      defaultTableHeaders.forEach(ele => {
        let { ...element } = ele;
        element.label = ele.header;
        element.property = ele.key;
        delete element.width;
        element = { ...element, headerColor, headerOpacity, valign };
        pdfHeader.push(element);
      });

      tableData.columns = [...pdfHeader];
      tableData.columnSize = [25, 80, 50, 95, 70, 65, 40, 60, 70];

      // table rows for pdf
      let pdfRows = [];
      Object.keys(defaultRowData).forEach(key => {
        pdfRows = [...pdfRows, Object.values(defaultRowData[key])];
      });
      tableData.rows = [...pdfRows];
      break;
    }
  }
  tableData.type = requestFormat;
  return tableData;
};

/**
 * Delete All Project's Safety Card
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteAllProjectSafetyCard = async (projectId, deletedAt) => {
  return safetyCardModel.updateMany(
    { project: projectId },
    {
      $set: deletedAt,
    }
  );
};

/**
 *
 * @param {*} req
 * @param {*} reqData
 * @param {*} severity
 * @param {*} likelihood
 * @returns
 */
exports.changeSafetyCard = async (req, reqData) => {
  let severity;
  let likelihood;
  if (req.body.cardType == 'unsafe' || req.body.cardType == 'incident') {
    // eslint-disable-next-line no-undef
    [severity, likelihood] = await Promise.all([
      severityService.getSeverityById(req.body.severity),
      likelihoodService.getLikelihoodById(req.body.likelihood),
    ]);
  }
  const keys = Object.keys(reqData);
  for (const key of keys) {
    const value = req.body[key];
    reqData[key] = value !== '' ? value : null;
  }

  reqData.riskFactor =
    req.body.cardType == 'unsafe' || req.body.cardType == 'incident'
      ? severity.severityValue * likelihood.likelihoodValue
      : null;
  reqData.actionsTaken = req.body.actionsTaken || '';
  reqData.status = req.body.status || 'open';
  reqData.account = req.userData.account;
  reqData.createdBy = req.userData.id;
  reqData.cardType = req.body.cardType;
  reqData.images = req.body.images;

  return reqData;
};

/**
 * Calculate Risk Factor
 *
 * @param {*} req
 * @returns
 */
exports.calculateRiskFactor = async req => {
  let severity;
  let likelihood;
  // eslint-disable-next-line no-undef
  [severity, likelihood] = await Promise.all([
    severityService.getSeverityById(req.body.severity),
    likelihoodService.getLikelihoodById(req.body.likelihood),
  ]);
  return severity.severityValue * likelihood.likelihoodValue;
};

/**
 * Calculate Sync Risk Factor
 *
 * @param {*} req
 * @returns
 */
exports.calculateSyncRiskFactor = async req => {
  let severity;
  let likelihood;
  // eslint-disable-next-line no-undef
  [severity, likelihood] = await Promise.all([
    severityService.getSeverityById(req.severity),
    likelihoodService.getLikelihoodById(req.likelihood),
  ]);
  return severity.severityValue * likelihood.likelihoodValue;
};

/**
 * Get Pre Created Location In Database
 *
 * @param {*} req
 * @returns
 */
exports.getlocation = async req => {
  let reqData = req.body;
  let project = reqData.project;
  let title = reqData.location;

  const locationData = {
    title: reqData.location,
    project: reqData.project,
    account: req.userData.account,
    isDefault: true,
  };
  const filterData = {
    title,
    project,
    account: req.userData.account.toString(),
    isDefault: true,
  };
  const exist = await locationService.getLocationByName(filterData);

  if (exist) {
    return exist._id;
  } else {
    const createdLocation = await locationService.createLocation(locationData);
    return commonUtils.toObjectId(createdLocation._id);
  }
};

/**
 * Get Pre Created Default Project In Database
 *
 * @param {*} req
 * @returns
 */
exports.getDefaultProject = async req => {
  if (!commonUtils.isValidId(req.body.defaultProject)) {
    const exist = await projectService.getProjectByName(
      req.body.defaultProject,
      req.userData.account,
      true
    );

    if (exist) {
      return exist._id;
    } else {
      const newReq = {
        title: req.body.defaultProject,
        account: req.userData.account,
        isActive: true,
        isDefault: true,
        defaultIdentifier: 'default',
      };
      const createdProject = await projectService.createProject(newReq);
      return commonUtils.toObjectId(createdProject._id);
    }
  } else {
    return req.body.defaultProject;
  }
};

/**
 * Get Default Sync Up Project
 *
 * @param {*} req
 * @param {*} account
 * @returns
 */
exports.getDefaultSyncUpProject = async (req, account) => {
  if (!commonUtils.isValidId(req.defaultProject)) {
    const exist = await projectService.getProjectByName(req.defaultProject, account, true);

    if (exist) {
      return exist._id;
    } else {
      const newReq = {
        title: req.defaultProject,
        account: account,
        isActive: true,
        isDefault: true,
        defaultIdentifier: 'default',
      };
      const createdProject = await projectService.createProject(newReq);
      return commonUtils.toObjectId(createdProject._id);
    }
  } else {
    return req.defaultProject;
  }
};

/**
 * Get Sync Up location In Database
 *
 * @param {*} req
 * @param {*} account
 * @returns
 */
exports.getSyncUplocation = async (req, account) => {
  let reqData = req;
  let project = reqData.project;
  let title = reqData.location;

  const locationData = {
    title: reqData.location,
    project: reqData.project,
    account: account,
    isDefault: true,
  };
  const filterData = {
    title,
    project,
    account: account,
    isDefault: true,
  };
  const exist = await locationService.getLocationByName(filterData);

  if (exist) {
    return exist._id;
  } else {
    const createdLocation = await locationService.createLocation(locationData);
    return commonUtils.toObjectId(createdLocation._id);
  }
};

/**
 * Get Pre Created Default Project In Database
 *
 * @param {*} req
 * @param {*} account
 * @returns
 */
exports.getSyncDefaultProject = async (req, account) => {
  if (!commonUtils.isValidId(req.defaultProject)) {
    const exist = await projectService.getProjectByName(req.defaultProject, account, true);

    if (exist) {
      return exist._id;
    } else {
      const newReq = {
        title: req.defaultProject,
        account: account,
        isActive: true,
        isDefault: true,
        defaultIdentifier: 'default',
      };
      const createdProject = await projectService.createProject(newReq);
      return commonUtils.toObjectId(createdProject._id);
    }
  } else {
    return req.defaultProject;
  }
};

/**
 * Get QHSC Card Count
 *
 * @param {*} filter
 * @returns
 */
exports.qhscCardCount = async filter => {
  const response = safetyCardModel
    .aggregate([
      {
        $match: filter,
      },
      {
        $facet: {
          cardTypeCounts: [
            {
              $group: {
                _id: '$cardType',
                count: { $sum: 1 },
              },
            },
            {
              $project: {
                _id: 0,
                cardType: '$_id',
                count: 1,
              },
            },
          ],
          totalCardCount: [
            {
              $group: {
                _id: null,
                totalCount: { $sum: 1 },
              },
            },
            {
              $project: {
                _id: 0,
                totalCount: 1,
              },
            },
          ],
          cardStatusCount: [
            {
              $group: {
                _id: '$status',
                count: { $sum: 1 },
              },
            },
            {
              $project: {
                _id: 0,
                status: '$_id',
                count: 1,
              },
            },
          ],
        },
      },
    ])
    .exec();

  return response
    .then(result => {
      const { cardTypeCounts, totalCardCount, cardStatusCount } = result[0];

      // Initialize the cardStatusCount with zero counts
      const cardStatusCountMap = safetyCardStatusOrder.reduce((acc, status) => {
        acc[status] = 0;
        return acc;
      }, {});

      // Update the cardStatusCountMap with actual counts
      cardStatusCount.forEach(({ status, count }) => {
        cardStatusCountMap[status] = count;
      });
      cardStatusCountMap.submitted += cardStatusCountMap['submitted(client)']
        ? cardStatusCountMap['submitted(client)']
        : 0;

      // Transform the cardStatusCountMap back to an array
      const formattedCardStatusCount = safetyCardStatusOrder.map(status => ({
        status,
        count: cardStatusCountMap[status],
      }));

      // Sort cardTypeCounts based on the desired order
      cardTypeCounts.sort(
        (existing, newer) =>
          safetyCardOrder.indexOf(existing.cardType) - safetyCardOrder.indexOf(newer.cardType)
      );

      return {
        cardTypeCounts,
        totalCardCount: totalCardCount.length > 0 ? totalCardCount[0].totalCount : 0,
        cardStatusCount: formattedCardStatusCount,
      };
    })
    .catch(err => {
      return err.message;
    });
};

/**
 * Get QHSC Card Risk Of Incident Count
 *
 * @param {*} filter
 * @returns
 */
exports.qhscCardRiskOfIncidentCount = async filter => {
  return await safetyCardModel.aggregate([
    {
      $match: filter,
    },
    {
      $addFields: {
        riskFactorNum: { $toInt: '$riskFactor' },
      },
    },
    {
      $addFields: {
        cardType: {
          $switch: {
            branches: [
              {
                case: {
                  $and: [
                    { $gte: ['$riskFactorNum', global.constant.RISK_OF_INCIDENT[0].min] },
                    { $lte: ['$riskFactorNum', global.constant.RISK_OF_INCIDENT[0].max] },
                  ],
                },
                then: 'Low',
              },
              {
                case: {
                  $and: [
                    { $gte: ['$riskFactorNum', global.constant.RISK_OF_INCIDENT[1].min] },
                    { $lte: ['$riskFactorNum', global.constant.RISK_OF_INCIDENT[1].max] },
                  ],
                },
                then: 'Medium',
              },
              {
                case: {
                  $and: [
                    { $gte: ['$riskFactorNum', global.constant.RISK_OF_INCIDENT[2].min] },
                    { $lte: ['$riskFactorNum', global.constant.RISK_OF_INCIDENT[2].max] },
                  ],
                },
                then: 'High',
              },
            ],
            default: global.constant.UNKNOWN,
          },
        },
      },
    },
    {
      $match: {
        cardType: { $ne: global.constant.UNKNOWN },
      },
    },
    {
      $group: {
        _id: '$cardType',
        count: { $sum: 1 },
      },
    },
    {
      $project: {
        _id: 0,
        cardType: '$_id',
        count: 1,
      },
    },
    {
      $group: {
        _id: null,
        cardTypeMap: {
          $push: {
            k: '$cardType',
            v: '$count',
          },
        },
      },
    },
    {
      $project: {
        _id: 0,
        cardTypeObj: {
          $arrayToObject: '$cardTypeMap',
        },
      },
    },
    {
      $project: {
        riskOfIncidentCount: [
          {
            cardType: 'Low',
            count: { $ifNull: ['$cardTypeObj.Low', 0] },
          },
          {
            cardType: 'Medium',
            count: { $ifNull: ['$cardTypeObj.Medium', 0] },
          },
          {
            cardType: 'High',
            count: { $ifNull: ['$cardTypeObj.High', 0] },
          },
        ],
        totalCardCount: {
          $add: [
            { $ifNull: ['$cardTypeObj.Low', 0] },
            { $ifNull: ['$cardTypeObj.Medium', 0] },
            { $ifNull: ['$cardTypeObj.High', 0] },
          ],
        },
      },
    },
  ]);
};

/**
 * Get QHSC Card Type Of Incident Count
 *
 * @param {*} filter
 * @returns
 */
exports.qhscCardTypeOfIncidentCount = async filter => {
  const allTypes = await Type.find({}, { _id: 1, title: 1 }).lean();

  const safetyCardCounts = await safetyCardModel.aggregate([
    {
      $match: filter,
    },
    {
      $group: {
        _id: '$type',
        count: { $sum: 1 },
      },
    },
  ]);

  // eslint-disable-next-line no-undef
  const countsMap = new Map(
    safetyCardCounts.map(safetyCard => [String(safetyCard._id), safetyCard.count])
  );

  const result = allTypes.map(type => ({
    type: type.title,
    count: countsMap.get(String(type._id)) || 0,
  }));

  const total = result.reduce((sum, item) => sum + item.count, 0);

  return {
    total,
    data: result,
  };
};

exports.prepareCardLogs = async (requestData, type, userId, cardId = null) => {
  const getSafetyCard = cardId ? await this.getSafetyCardById(cardId) : {};
  const countLogs = getSafetyCard?.cardLogs?.length || 0;
  let commonCardLog = {
    user: userId,
    time: new Date(),
    status: 'open',
    action: '',
    version: '',
  };
  switch (type) {
    case 'create':
      requestData.cardLogs = {
        ...commonCardLog,
        action: 'Submitted',
        version: 'v1',
      };
      return requestData;
    case 'update':
      commonCardLog.status =
        getSafetyCard.status == 'submitted' ? 'submitted(client)' : getSafetyCard.status;
      commonCardLog.action = requestData.explanationOfChanges ?? 'Updated';
      commonCardLog.version = `v${countLogs + 1}`;
      this.updateCardLogs(cardId, commonCardLog);
  }
};

exports.updateCardLogs = async (id, requestData) => {
  return safetyCardModel.findByIdAndUpdate(id, { $push: { cardLogs: requestData } });
};

/**
 * Get Safety Cards Summary
 *
 * @param {*} project
 * @param {*} account
 * @returns
 */
exports.safetyCardsSummary = async filter => {
  return safetyCardModel.find(filter).populate({
    path: 'type',
    select: { title: 1 },
    strictPopulate: false,
  });
};

/**
 * Get Safety Cards Summary Count
 *
 * @param {*} response
 * @param {*} toolboxTalk
 * @param {*} dprDate
 * @returns
 */
exports.safetyCardsSummaryCount = async (response, toolboxTalk, dprDate, timezone) => {
  const todayDate = new Date(dprDate).toISOString().split('T')[0];

  const toolboxTalkCount = toolboxTalk.reduce(
    (acc, { createdAt }) => {
      const itemDate = commonUtils.convertUTCToLocalTimezone(
        createdAt,
        timezone,
        global.constant.DATE_FORMAT_YYYY_MM_DD
      );
      const isToday = itemDate === todayDate;
      const isPastDate = new Date(itemDate) < new Date(todayDate);

      if (isToday) {
        acc['today']++;
        acc.total++;
      }
      if (isPastDate) {
        acc['previous']++;
        acc.total++;
      }

      return acc;
    },
    { previous: 0, today: 0, total: 0 }
  );

  // eslint-disable-next-line no-undef
  const categories = new Set(global.constant.CARD_TYPE);

  response.forEach(({ cardType, type }) => {
    if (cardType) categories.add(cardType);
    if (cardType === 'incident' && type?.title) categories.add(type.title);
  });

  const counts = Object.fromEntries(
    [...categories].map(type => [type, { type, previous: 0, today: 0, open: 0, total: 0 }])
  );

  response.forEach(({ cardType, createdAt, status, type }) => {
    const itemDate = commonUtils.convertUTCToLocalTimezone(
      createdAt,
      timezone,
      global.constant.DATE_FORMAT_YYYY_MM_DD
    );
    const isToday = itemDate == todayDate;
    const isPastDate = new Date(itemDate) < new Date(todayDate);
    const isInDiscussion = status === 'in_discussion';
    const isSubmitted = status === 'submitted' || status === 'submitted(client)';

    const shouldUpdateCount = [
      'submitted',
      'submitted(client)',
      'in_discussion',
      'closed',
    ].includes(status);

    [cardType, cardType === 'incident' ? type?.title : null].filter(Boolean).forEach(category => {
      if (!counts[category])
        counts[category] = { type: category, previous: 0, today: 0, open: 0, total: 0 };

      if (isToday) {
        if (isInDiscussion || isSubmitted) {
          counts[category].open++;
        }
      }

      if (shouldUpdateCount) {
        if (isToday) {
          counts[category]['today']++;
          counts[category].total++;
        }
        if (isPastDate) {
          counts[category]['previous']++;
          counts[category].total++;
        }
      }
    });
  });

  return [
    { type: 'toolbox talks', ...toolboxTalkCount, open: '-' },
    ...[...categories].map(category => counts[category]),
  ];
};
