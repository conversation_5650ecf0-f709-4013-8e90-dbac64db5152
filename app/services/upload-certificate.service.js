const UploadCertificate = require('../models/upload-certificate.model');
const Member = require('../models/member.model');

// utils
const { toObjectId } = require('../utils/common.utils');
const aggregateComponentUtils = require('../utils/aggregate-component.utils');

/**
 * Upload Certificates
 *
 * @param {*} requestData
 * @returns
 */
exports.createUploadCertificate = async requestData => {
  return await UploadCertificate.create(requestData);
};

exports.getUserUploadCertificate = async filter => {
  return await UploadCertificate.find(filter);
};

exports.uploadCertificateInsertMany = async requestData => {
  return await UploadCertificate.insertMany(requestData);
};

/**
 * getbyId Upload Certificate
 *
 * @param {*} user
 * @returns
 */
exports.getUploadCertificateById = async user => {
  return await UploadCertificate.findOne({
    user,
  });
};

exports.getSingleUploadCertificateByFilter = async filter => {
  return await UploadCertificate.findOne(filter);
};

/**
 * get All project wise data
 *
 * @param {*} project
 * @returns
 */

exports.getAllProjectData = async () => {
  return await UploadCertificate.find({ project: 'all' }).lean().exec();
};
/**
 * get Upload Certificates
 *
 * @param {*} id
 * @returns
 */

exports.getUserCertificates = async filter => {
  let aggregateFilter = [
    {
      $match: filter,
    },
    aggregateComponentUtils.aggregateLookup('project'),
    aggregateComponentUtils.aggregateUnwind('project'),
    {
      $lookup: {
        from: 'certificates',
        localField: 'function',
        foreignField: 'function',
        pipeline: [
          {
            $project: {
              _id: 1,
              certificates: 1,
            },
          },
        ],
        as: 'certificate',
      },
    },
    { $unwind: '$certificate' },
    {
      $lookup: {
        from: 'certificate-types',
        localField: 'certificate.certificates',
        foreignField: '_id',
        pipeline: [
          {
            $match: {
              deletedAt: null,
            },
          },
          {
            $project: {
              _id: 1,
              name: 1,
              validityDate: 1,
              createdAt: 1,
            },
          },
        ],
        as: 'certificate.certificates',
      },
    },
    { $unwind: '$certificate.certificates' },
    {
      $group: {
        _id: '$certificate.certificates._id',
        certificate: { $first: '$certificate.certificates' },
        project: { $push: '$project' },
      },
    },
    {
      $addFields: {
        projectCertificate: {
          $concat: [
            { $ifNull: ['$certificate.name', ''] },
            ' (',
            {
              $ifNull: [
                {
                  $reduce: {
                    input: '$project',
                    initialValue: '',
                    in: {
                      $concat: [
                        '$$value',
                        { $cond: [{ $ne: ['$$value', ''] }, ', ', ''] },
                        '$$this.title',
                      ],
                    },
                  },
                },
                '',
              ],
            },
            ')',
          ],
        },
      },
    },
    {
      $sort: { 'certificate.createdAt': 1 },
    },
  ];

  return Member.aggregate(aggregateFilter);
};

/**
 * Update Approval Status
 *
 * @param {*} id
 * @param {*} update
 * @returns
 */
exports.updateDetail = async (id, requestData) =>
  UploadCertificate.findByIdAndUpdate(id, { $set: requestData }, { new: true });

/**
 * Approval Certificate List
 *
 * @param {*} filter
 * @returns
 */

exports.certificateList = async (filter, search, page, perPage) => {
  const regexSearch = search.trim().split(/\s+/).join('|');
  const regexPattern = new RegExp(regexSearch, 'i');
  let aggregateFilter = [
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        as: 'projects',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
            },
          },
        ],
      },
    },
    { $unwind: '$projects' },
    {
      $lookup: {
        from: 'functions',
        localField: 'function',
        foreignField: '_id',
        as: 'functionDetails',
      },
    },
    { $unwind: '$functionDetails' },
    {
      $addFields: {
        function: {
          functionId: '$functionDetails._id',
          functionName: '$functionDetails.functionName',
        },
      },
    },
    {
      $unset: 'functionDetails',
    },
    {
      $lookup: {
        from: 'users',
        localField: 'user',
        foreignField: '_id',
        as: 'user',
        pipeline: [
          {
            $project: {
              _id: 1,
              fullName: {
                $concat: [
                  '$lastName',
                  ' ',
                  {
                    $cond: {
                      if: {
                        $or: [{ $eq: ['$callingName', null] }, { $eq: ['$callingName', ''] }],
                      },
                      then: '$firstName',
                      else: '$callingName',
                    },
                  },
                ],
              },
              email: 1,
              profileImage: 1,
            },
          },
        ],
      },
    },
    { $unwind: '$user' },
    {
      $lookup: {
        from: 'certificates',
        localField: 'project',
        foreignField: 'project',
        pipeline: [
          {
            $project: {
              _id: 1,
              certificates: 1,
            },
          },
        ],
        as: 'certificate',
      },
    },
    { $unwind: '$certificate' },
    {
      $lookup: {
        from: 'certificate-types',
        localField: 'certificate.certificates',
        foreignField: '_id',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
              validityDate: 1,
            },
          },
        ],
        as: 'certificate.certificates',
      },
    },
    { $unwind: '$certificate.certificates' },
    {
      $lookup: {
        from: 'upload-certificates',
        localField: 'user._id',
        foreignField: 'user',
        pipeline: [
          {
            $project: {
              _id: 1,
              files: 1,
            },
          },
        ],
        as: 'certificate.uploads',
      },
    },
    { $unwind: '$certificate.uploads' },
    { $unwind: '$certificate.uploads.files' },
    {
      $redact: {
        $cond: {
          if: {
            $eq: ['$certificate.certificates._id', '$certificate.uploads.files.certificate'],
          },
          then: '$$KEEP',
          else: '$$PRUNE',
        },
      },
    },
    {
      $addFields: {
        certificate: { $ifNull: ['$certificate', {}] },
      },
    },
    {
      $unset: ['deletedBy', 'deletedAt', 'isDeleted', '__v'],
    },
    {
      $match: {
        $or: [{ 'user.fullName': regexPattern }],
      },
    },
    {
      $match: {
        $or: [{ 'certificate.uploads.files.status': 'pending' }],
      },
    },
    {
      $project: {
        createdAt: 0,
        updatedAt: 0,
        project: 0,
        functionsDetails: 0,
      },
    },
    {
      $sort: {
        'certificate.validityDate': 1,
      },
    },
    {
      $skip: page * perPage,
    },
    {
      $limit: perPage,
    },
  ];
  return Member.aggregate(aggregateFilter);
};

/**
 * Update Approval Status
 *
 * @param {*} id
 * @param {*} userId
 * @param {*} update
 * @returns
 */
exports.updateApproval = async (fileId, userId, update) => {
  const query = { $and: [{ user: userId }, { 'files._id': toObjectId(fileId) }] };
  const updateObject = {
    $set: {
      'files.$.status': update.status,
    },
  };
  if (update.status !== 'pending') {
    updateObject.$set['files.$.rejectReason'] = update.reason;
  }
  return await UploadCertificate.findOneAndUpdate(query, updateObject, { new: true });
};

exports.getUploadedCertificates = async (filter, page, perPage, sort) => {
  let aggregateCertificates = [
    {
      $match: filter,
    },
    {
      $skip: parseInt(page) * parseInt(perPage),
    },
    {
      $limit: parseInt(perPage),
    },
    {
      $sort: { createdAt: sort },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'user',
        foreignField: '_id',
        as: 'user',
        pipeline: [
          {
            $project: {
              _id: 1,
              callingName: 1,
              firstName: 1,
              lastName: 1,
              email: 1,
              profileImage: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$user',
    },
    {
      $lookup: {
        from: 'accounts',
        localField: 'account',
        foreignField: '_id',
        as: 'account',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$account',
    },
    {
      $lookup: {
        from: 'certificate-types',
        localField: 'certificateType',
        foreignField: '_id',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
            },
          },
        ],
        as: 'certificateType',
      },
    },
    {
      $unwind: '$certificateType',
    },
    {
      $unset: ['createdBy', 'updatedBy', 'updatedAt', 'deletedBy', 'deletedAt', '__v'],
    },
  ];

  return UploadCertificate.aggregate(aggregateCertificates);
};

/**
 * Delete Certificate
 *
 * @param {*} id
 * @param {*} updateData
 * @returns
 */
exports.deleteCertificate = async (id, updateData) => {
  return UploadCertificate.findByIdAndUpdate(id, { $set: updateData });
};

/**
 * Update Certificate by filter
 *
 * @param {*} filter
 * @param {*} requestData
 * @returns
 */
exports.updateCertificateByFilter = async (filter, requestData) =>
  UploadCertificate.updateMany(filter, { $set: requestData }, { new: true });

/**
 * Get Certificates By User
 *
 * @param {*} filter
 * @returns
 */
exports.getCertificatesByUser = async filter => {
  return await UploadCertificate.find(filter);
};

/**
 * Change Is Active Status
 *
 * @returns
 */
exports.changeIsActiveStatus = async () => {
  return await UploadCertificate.updateMany(
    { isActive: { $exists: false } },
    { $set: { isActive: true } }
  );
};
