const PmOrder = require('../models/pm-order.model');
const pmOrderManageEquipment = require('../models/pm-order-manage-equipment.model');
const Equipment = require('../models/equipment.model');
const EquipmentOrderHistory = require('../models/equipment-order-history.model');
const ReturnOrderHistory = require('../models/return-order-history.model');
const responseUtils = require('../utils/response.utils');
const constantUtils = require('../utils/constants.utils');
const aggregateComponentUtils = require('../utils/aggregate-component.utils');

/**
 * Get PM Orders
 *
 * @param {*} filter
 * @param {*} page
 * @param {*} perPage
 * @param {*} sort
 * @param {*} search
 * @returns
 */
exports.getPMOrderList = async (filter, page, perPage, sort, search) => {
  let equipmentHistoryStatus = null;
  if (['pre-transit'].includes(filter?.status)) {
    equipmentHistoryStatus = ['linked'];
  } else if (filter?.status === 'in-transit') {
    equipmentHistoryStatus = ['in-transit', 'pre-check-in'];
  }

  let groupInnerCond = this.checkOrderStatusAndModifyList(filter.status);

  filter = this.checkAndFilterStatus(filter);
  let { status, ...filterData } = filter;

  if (typeof status === 'object' && status['$in'].includes('in-transit')) {
    const getPMOrders = await PmOrder.find(
      { ...filterData, status: { $in: ['pre-transit', 'partially-pre-transit'] } },
      { _id: 1 }
    );
    for (let pmOrderData of getPMOrders) {
      const getHostory = await EquipmentOrderHistory.findOne({
        pmOrder: pmOrderData._id,
        status: { $in: ['in-transit', 'pre-check-in'] },
        deletedAt: null,
      });

      if (getHostory) {
        status['$in'].push('pre-transit');
        break;
      }
    }
  }

  let aggregateFunction = [
    {
      $match: filterData,
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        as: 'project',
        pipeline: [{ $project: { title: 1 } }],
      },
    },
    {
      $unwind: '$project',
    },
    {
      $lookup: {
        from: 'accounts',
        localField: 'account',
        foreignField: '_id',
        pipeline: [{ $project: { _id: 1, name: 1 } }],
        as: 'account',
      },
    },
    {
      $unwind: '$account',
    },
    {
      $lookup: {
        from: 'users',
        localField: 'createdBy',
        foreignField: '_id',
        pipeline: [{ $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } }],
        as: 'createdBy',
      },
    },
    {
      $unwind: '$createdBy',
    },
    {
      $lookup: {
        from: 'pm-order-manage-equipments',
        localField: '_id',
        foreignField: 'pmOrder',
        pipeline: [
          {
            $match: {
              status: status,
              deletedAt: null,
            },
          },
        ],
        as: 'pmManageEquipmentArray',
      },
    },
    {
      $lookup: {
        from: 'pm-order-manage-equipments',
        localField: '_id',
        foreignField: 'pmOrder',
        pipeline: [
          {
            $match: {
              status: status,
              deletedAt: null,
            },
          },
        ],
        as: 'pmManageEquipment',
      },
    },
    {
      $unwind: '$pmManageEquipment',
    },
    {
      $lookup: {
        from: 'equipment-types',
        localField: 'pmManageEquipment.equipmentType',
        foreignField: '_id',
        as: 'equipmentTypeDetails',
        pipeline: [
          ...(search
            ? [
                {
                  $match: {
                    type: {
                      $regex: search,
                      $options: 'i',
                    },
                    account: filter.account,
                  },
                },
              ]
            : []),
        ],
      },
    },
    {
      $unwind: '$equipmentTypeDetails',
    },
    {
      $lookup: {
        from: 'equipment-quantity-types',
        localField: 'equipmentTypeDetails.quantityType',
        foreignField: '_id',
        as: 'equipmentQuantityTypeDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
              priceType: 1,
              quantityType: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$equipmentQuantityTypeDetails',
    },
    {
      $lookup: {
        from: 'equipment',
        localField: 'equipmentTypeDetails._id',
        foreignField: 'equipmentType',
        as: 'associatedEquipment',
        pipeline: [
          {
            $match: {
              equipmentImage: { $exists: true, $ne: [] },
            },
          },
          { $sort: { createdAt: -1 } },
          {
            $addFields: {
              lastEquipmentImage: {
                $ifNull: [{ $arrayElemAt: ['$equipmentImage', -1] }, null],
              },
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'equipment',
        localField: 'pmManageEquipment.equipment',
        foreignField: '_id',
        as: 'equipmentDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
              equipmentNumber: 1,
              qrCode: 1,
              equipmentImage: 1,
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'equipment-order-histories',
        localField: 'pmManageEquipment.equipment',
        foreignField: 'equipment',
        let: { pmOrderManageEquipment: '$pmManageEquipment._id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$pmOrderManageEquipment', '$$pmOrderManageEquipment'] },
                  equipmentHistoryStatus ? { $in: ['$status', equipmentHistoryStatus] } : {},
                ],
              },
              deletedAt: null,
            },
          },
          {
            $group: {
              _id: '$equipment',
              equipment: { $first: '$equipment' },
              status: { $first: '$status' },
              wmDispatchQuantity: { $sum: '$wmDispatchQuantity' },
              pmReceivedQuantity: { $sum: '$pmReceivedQuantity' },
              pmDispatchQuantity: { $sum: '$pmDispatchQuantity' },
              wmReceivedQuantity: { $sum: '$wmReceivedQuantity' },
            },
          },
          {
            $lookup: {
              from: 'equipment',
              localField: 'equipment',
              foreignField: '_id',
              as: 'equipment',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    name: 1,
                    equipmentNumber: 1,
                    serialNumber: 1,
                    equipmentImage: 1,
                  },
                },
              ],
            },
          },
          {
            $unwind: '$equipment',
          },
        ],
        as: 'equipmentOrderHistory',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'pmManageEquipment.pmComments.user',
        foreignField: '_id',
        as: 'pmCommentUsers',
      },
    },
    {
      $addFields: {
        'pmManageEquipment.pmComments': {
          $map: {
            input: '$pmManageEquipment.pmComments',
            as: 'comment',
            in: {
              comment: '$$comment.comment',
              time: '$$comment.time',
              status: '$$comment.status',
              user: {
                $let: {
                  vars: {
                    userObj: {
                      $arrayElemAt: [
                        '$pmCommentUsers',
                        {
                          $indexOfArray: ['$pmCommentUsers._id', '$$comment.user'],
                        },
                      ],
                    },
                  },
                  in: {
                    callingName: '$$userObj.callingName',
                    firstName: '$$userObj.firstName',
                    lastName: '$$userObj.lastName',
                    _id: '$$userObj._id',
                  },
                },
              },
            },
          },
        },
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'pmManageEquipment.wmComments.user',
        foreignField: '_id',
        as: 'wmCommentUsers',
      },
    },
    {
      $addFields: {
        'pmManageEquipment.wmComments': {
          $map: {
            input: '$pmManageEquipment.wmComments',
            as: 'comment',
            in: {
              comment: '$$comment.comment',
              time: '$$comment.time',
              status: '$$comment.status',
              user: {
                $let: {
                  vars: {
                    userObj: {
                      $arrayElemAt: [
                        '$wmCommentUsers',
                        {
                          $indexOfArray: ['$wmCommentUsers._id', '$$comment.user'],
                        },
                      ],
                    },
                  },
                  in: {
                    callingName: '$$userObj.callingName',
                    firstName: '$$userObj.firstName',
                    lastName: '$$userObj.lastName',
                    _id: '$$userObj._id',
                  },
                },
              },
            },
          },
        },
      },
    },
    {
      $group: {
        _id: '$_id',
        orderNumber: {
          $first: '$orderNumber',
        },
        orderBy: {
          $first: {
            id: '$createdBy._id',
            callingName: '$createdBy.callingName',
            firstName: '$createdBy.firstName',
            lastName: '$createdBy.lastName',
          },
        },
        equipmentTypeData: {
          $addToSet: groupInnerCond,
        },
        totalRequestedQuantity: {
          $sum: '$pmManageEquipment.pmRequestedQuantity',
        },
        totalItems: {
          $first: {
            $size: '$pmManageEquipmentArray',
          },
        },
        account: {
          $first: '$account',
        },
        project: {
          $first: '$project',
        },
        status: {
          $first: '$status',
        },
        createdAt: {
          $first: '$createdAt',
        },
        updatedAt: {
          $first: '$updatedAt',
        },
      },
    },
    {
      $sort: {
        createdAt: sort,
      },
    },
  ];

  aggregateFunction.push(
    {
      $skip: parseInt(page) * parseInt(perPage),
    },
    {
      $limit: parseInt(perPage),
    }
  );

  return await PmOrder.aggregate(aggregateFunction);
};

exports.checkOrderStatusAndModifyList = status => {
  let groupData = {
    equipmentTypeId: '$equipmentTypeDetails._id',
    typeName: '$equipmentTypeDetails.type',
    isTemporary: { $ifNull: ['$equipmentTypeDetails.isTemporary', false] },
    equipmentTypeImage: {
      $first: '$associatedEquipment.lastEquipmentImage',
    },
    equipmentQuantityType: '$equipmentQuantityTypeDetails.quantityType',
    equipmentPriceType: '$equipmentQuantityTypeDetails.priceType',
    manageId: '$pmManageEquipment._id',
    comments: {
      wmComments: '$pmManageEquipment.wmComments',
      pmComments: '$pmManageEquipment.pmComments',
    },
    pmRequestedQuantity: '$pmManageEquipment.pmRequestedQuantity',
    wmApprovedQuantity: '$pmManageEquipment.wmApprovedQuantity',
    wmDispatchQuantity: '$pmManageEquipment.wmDispatchQuantity',
    pmReceivedQuantity: '$pmManageEquipment.pmReceivedQuantity',
    pmDispatchQuantity: '$pmManageEquipment.pmDispatchQuantity',
    status: '$pmManageEquipment.status',
    fromPeriod: '$pmManageEquipment.fromPeriod',
    toPeriod: '$pmManageEquipment.toPeriod',
    linkedEquipment: '$equipmentOrderHistory',
  };

  let groupCondition = '';
  if (['requested', 'approved'].includes(status)) {
    groupCondition = groupData;
  } else {
    groupCondition = {
      $cond: {
        if: { $ne: ['$equipmentOrderHistory', []] },
        then: groupData,
        else: null,
      },
    };
  }
  return groupCondition;
};

/**
 * Get PM Order Manage Data By Filter
 *
 * @param {*} filter
 * @param {*} page
 * @param {*} perPage
 * @param {*} sort
 * @param {*} search
 * @returns
 */
exports.getPMOrderManageDataByOrderId = async (filter, page, perPage, sort, search = '') => {
  let equipmentHistoryStatus = null;
  if (['pre-transit'].includes(filter?.status)) {
    equipmentHistoryStatus = ['linked'];
  } else if (filter?.status === 'in-transit') {
    equipmentHistoryStatus = ['in-transit', 'pre-check-in'];
  }

  let groupInnerCond = this.checkOrderStatusAndModify(filter.status);

  filter = this.checkAndFilterStatus(filter);

  if (typeof filter.status === 'object' && filter.status['$in'].includes('in-transit')) {
    const getPMOrder = await PmOrder.findOne(
      { _id: filter.pmOrder, status: { $in: ['pre-transit', 'partially-pre-transit'] } },
      { _id: 1 }
    );
    if (getPMOrder) {
      const getHostory = await EquipmentOrderHistory.findOne({
        pmOrder: getPMOrder._id,
        status: { $in: ['in-transit', 'pre-check-in'] },
        deletedAt: null,
      });

      if (getHostory) {
        filter.status['$in'].push('pre-transit');
      }
    }
  }

  let aggregateFunction = [
    {
      $match: filter,
    },
    {
      $skip: parseInt(page) * parseInt(perPage),
    },
    {
      $limit: parseInt(perPage),
    },
    {
      $lookup: {
        from: 'pm-orders',
        localField: 'pmOrder',
        foreignField: '_id',
        as: 'pmOrderData',
      },
    },
    {
      $unwind: '$pmOrderData',
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'pmOrderData.project',
        foreignField: '_id',
        as: 'projectData',
        pipeline: [{ $project: { title: 1 } }],
      },
    },
    {
      $unwind: '$projectData',
    },
    {
      $lookup: {
        from: 'accounts',
        localField: 'account',
        foreignField: '_id',
        as: 'account',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$account',
    },
    {
      $lookup: {
        from: 'users',
        localField: 'createdBy',
        foreignField: '_id',
        pipeline: [
          {
            $project: {
              _id: 1,
              callingName: 1,
              firstName: 1,
              lastName: 1,
              profileImage: 1,
              email: 1,
            },
          },
        ],
        as: 'createdBy',
      },
    },
    {
      $unwind: {
        path: '$createdBy',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: 'equipment',
        localField: 'equipmentType',
        foreignField: 'equipmentType',
        as: 'associatedEquipment',
        pipeline: [
          {
            $match: {
              equipmentImage: { $exists: true, $ne: [] },
            },
          },
          { $sort: { createdAt: -1 } },
          {
            $addFields: {
              lastEquipmentImage: { $arrayElemAt: ['$equipmentImage', -1] },
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'equipment-types',
        localField: 'equipmentType',
        foreignField: '_id',
        as: 'equipmentTypeDetails',
      },
    },
    {
      $unwind: '$equipmentTypeDetails',
    },
    {
      $match: {
        'equipmentTypeDetails.type': {
          $regex: search,
          $options: 'i',
        },
      },
    },
    {
      $sort: sort,
    },
    {
      $lookup: {
        from: 'equipment-categories',
        localField: 'equipmentTypeDetails.equipmentCategory',
        foreignField: '_id',
        as: 'equipmentCategoryDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
              abbreviation: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$equipmentCategoryDetails',
    },
    {
      $lookup: {
        from: 'equipment-units',
        localField: 'equipmentTypeDetails.equipmentUnit',
        foreignField: '_id',
        as: 'equipmentUnitDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
              abbreviation: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$equipmentUnitDetails',
    },
    {
      $lookup: {
        from: 'currency-units',
        localField: 'equipmentTypeDetails.currencyUnit',
        foreignField: '_id',
        as: 'currencyUnitDetails',
        pipeline: [
          {
            $project: {
              _id: 0,
              name: 1,
              symbol: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$currencyUnitDetails',
    },
    {
      $lookup: {
        from: 'equipment-quantity-types',
        localField: 'equipmentTypeDetails.quantityType',
        foreignField: '_id',
        as: 'equipmentQuantityTypeDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
              priceType: 1,
              quantityType: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$equipmentQuantityTypeDetails',
    },
    {
      $lookup: {
        from: 'hs-codes',
        localField: 'equipmentTypeDetails.hsCode',
        foreignField: '_id',
        as: 'hsCodeDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
              code: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$hsCodeDetails',
    },
    {
      $lookup: {
        from: 'equipment',
        localField: 'equipment',
        foreignField: '_id',
        as: 'equipmentDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
              equipmentNumber: 1,
              equipmentImage: 1,
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'equipment-order-histories',
        localField: 'equipment',
        foreignField: 'equipment',
        let: { pmOrderManageEquipment: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$deletedAt', null] },
                  { $eq: ['$pmOrderManageEquipment', '$$pmOrderManageEquipment'] },
                  equipmentHistoryStatus ? { $in: ['$status', equipmentHistoryStatus] } : {},
                ],
              },
            },
          },
          {
            $project: {
              _id: 0,
              equipment: 1,
              status: 1,
              wmDispatchQuantity: 1,
              pmReceivedQuantity: 1,
              pmDispatchQuantity: 1,
              wmReceivedQuantity: 1,
            },
          },
          {
            $lookup: {
              from: 'equipment',
              localField: 'equipment',
              foreignField: '_id',
              as: 'equipment',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    name: 1,
                    equipmentNumber: 1,
                    serialNumber: 1,
                    equipmentImage: 1,
                    certificateType: 1,
                  },
                },
              ],
            },
          },
          {
            $unwind: '$equipment',
          },
        ],
        as: 'equipmentOrderHistory',
      },
    },
    {
      $addFields: {
        rentalDays: {
          $ceil: {
            $divide: [{ $subtract: ['$toPeriod', '$fromPeriod'] }, 86400000],
          },
        },
        totalPrice: {
          $round: { $multiply: ['$pmRequestedQuantity', '$equipmentTypeDetails.price'] },
        },
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'pmComments.user',
        foreignField: '_id',
        as: 'pmCommentUsers',
      },
    },
    {
      $addFields: {
        pmComments: {
          $map: {
            input: '$pmComments',
            as: 'comment',
            in: {
              comment: '$$comment.comment',
              time: '$$comment.time',
              status: '$$comment.status',
              user: {
                $let: {
                  vars: {
                    userObj: {
                      $arrayElemAt: [
                        '$pmCommentUsers',
                        {
                          $indexOfArray: ['$pmCommentUsers._id', '$$comment.user'],
                        },
                      ],
                    },
                  },
                  in: {
                    callingName: '$$userObj.callingName',
                    firstName: '$$userObj.firstName',
                    lastName: '$$userObj.lastName',
                    _id: '$$userObj._id',
                  },
                },
              },
            },
          },
        },
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'wmComments.user',
        foreignField: '_id',
        as: 'wmCommentUsers',
        pipeline: [
          {
            $project: {
              _id: 1,
              callingName: 1,
              firstName: 1,
              lastName: 1,
            },
          },
        ],
      },
    },
    {
      $addFields: {
        wmComments: {
          $map: {
            input: '$wmComments',
            as: 'comment',
            in: {
              comment: '$$comment.comment',
              time: '$$comment.time',
              status: '$$comment.status',
              user: {
                $let: {
                  vars: {
                    userObj: {
                      $arrayElemAt: [
                        '$wmCommentUsers',
                        {
                          $indexOfArray: ['$wmCommentUsers._id', '$$comment.user'],
                        },
                      ],
                    },
                  },
                  in: {
                    callingName: '$$userObj.callingName',
                    firstName: '$$userObj.firstName',
                    lastName: '$$userObj.lastName',
                    _id: '$$userObj._id',
                  },
                },
              },
            },
          },
        },
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'pmOrderData.comments.user',
        foreignField: '_id',
        as: 'pmOrderDataComments',
        pipeline: [
          {
            $project: {
              _id: 1,
              callingName: 1,
              firstName: 1,
              lastName: 1,
            },
          },
        ],
      },
    },
    {
      $addFields: {
        'pmOrderData.comments': {
          $map: {
            input: '$pmOrderData.comments',
            as: 'comment',
            in: {
              comment: '$$comment.comment',
              time: '$$comment.time',
              status: '$$comment.status',
              user: {
                $let: {
                  vars: {
                    userObj: {
                      $arrayElemAt: [
                        '$pmOrderDataComments',
                        {
                          $indexOfArray: ['$pmOrderDataComments._id', '$$comment.user'],
                        },
                      ],
                    },
                  },
                  in: {
                    callingName: '$$userObj.callingName',
                    firstName: '$$userObj.firstName',
                    lastName: '$$userObj.lastName',
                    _id: '$$userObj._id',
                  },
                },
              },
            },
          },
        },
      },
    },
    {
      $group: {
        _id: '$pmOrderData._id',
        orderNumber: { $first: '$pmOrderData.orderNumber' },
        orderBy: { $first: '$createdBy' },
        equipmentTypeData: {
          $push: groupInnerCond,
        },
        project: { $first: '$projectData' },
        status: { $first: '$pmOrderData.status' },
        pmComment: { $first: '$pmOrderData.comments' },
        totalRequestedQuantity: {
          $sum: '$pmRequestedQuantity',
        },
        totalItems: {
          $sum: 1,
        },
        fromDate: { $first: '$pmOrderData.fromDate' },
        toDate: { $first: '$pmOrderData.toDate' },
        createdAt: { $first: '$pmOrderData.createdAt' },
        updatedAt: { $first: '$pmOrderData.updatedAt' },
      },
    },
  ];

  return pmOrderManageEquipment.aggregate(aggregateFunction);
};

exports.checkOrderStatusAndModify = status => {
  let groupData = {
    pmOrderManageId: '$_id',
    equipmentTypeId: '$equipmentTypeDetails._id',
    equipmentType: '$equipmentTypeDetails.type',
    isTemporary: { $ifNull: ['$equipmentTypeDetails.isTemporary', false] },
    equipmentTypeImage: { $first: '$associatedEquipment.lastEquipmentImage' },
    equipmentCategory: '$equipmentCategoryDetails.name',
    quantityPriceType: '$equipmentQuantityTypeDetails.priceType',
    quantityType: '$equipmentQuantityTypeDetails.quantityType',
    currencyDetail: '$currencyUnitDetails',
    pmRequestedQuantity: '$pmRequestedQuantity',
    wmApprovedQuantity: '$wmApprovedQuantity',
    wmDispatchQuantity: '$wmDispatchQuantity',
    pmReceivedQuantity: '$pmReceivedQuantity',
    pmDispatchQuantity: '$pmDispatchQuantity',
    remainingApprovedQuantity: {
      $subtract: ['$wmApprovedQuantity', '$wmDispatchQuantity'],
    },
    remainingCheckInQuantity: {
      $subtract: ['$wmDispatchQuantity', '$pmReceivedQuantity'],
    },
    comments: {
      pmComments: '$pmComments',
      wmComments: '$wmComments',
    },
    rentalDays: { $sum: '$rentalDays' },
    fromPeriod: '$fromPeriod',
    toPeriod: '$toPeriod',
    price: '$equipmentTypeDetails.price',
    totalPrice: '$totalPrice',
    status: '$status',
    pmOrder: '$pmOrder',
    linkedEquipment: '$equipmentOrderHistory',
    account: '$account',
    createdAt: '$createdAt',
    updatedAt: '$updatedAt',
  };

  let groupCondition = '';
  if (status === 'approved' || typeof status === 'undefined') {
    groupCondition = groupData;
  } else {
    groupCondition = {
      $cond: {
        if: { $ne: ['$equipmentOrderHistory', []] },
        then: groupData,
        else: null,
      },
    };
  }
  return groupCondition;
};

/**
 * Check And Filter Status
 *
 * @param {*} filter
 * @returns
 */
exports.checkAndFilterStatus = filter => {
  if (['approved', 'pre-transit', 'in-transit', 'check-in'].includes(filter?.status)) {
    switch (filter.status) {
      case 'pre-transit':
        filter.status = { $in: ['pre-transit', 'partially-pre-transit'] };
        break;
      case 'in-transit':
        filter.status = { $in: ['in-transit', 'partially-in-transit'] };
        break;
      case 'approved':
        filter.status = {
          $in: ['approved', 'partially-pre-transit', 'partially-in-transit', 'partially-check-in'],
        };
        break;
      case 'check-in':
        filter.status = { $in: ['check-in', 'partially-check-in'] };
        break;
      default:
        // If none of the cases match, keep the original status
        break;
    }
  }
  return filter;
};

/**
 * Update Linked Equipment
 *
 * @param {*} id
 * @param {*} requestData
 * @param {*} session
 * @returns
 */
exports.updateEquipmentOnLinkEquipment = async (id, requestData, session) => {
  return await Equipment.findByIdAndUpdate(id, { $set: requestData }, { new: true, session });
};

/**
 * Get Linked Equipment By Order Detail By Filter
 *
 * @param {*} filter
 * @returns
 */
exports.getLinkedEquipmentByOrderDetailByFilter = async filter => {
  let aggregateFunction = [
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'equipment-order-histories',
        localField: 'equipment',
        foreignField: 'equipment',
        let: { pmOrderManageEquipment: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$pmOrderManageEquipment', '$$pmOrderManageEquipment'] },
                  { $eq: ['$status', 'linked'] },
                ],
              },
              deletedAt: null,
            },
          },
          {
            $group: {
              _id: '$equipment',
              equipment: { $first: '$equipment' },
              status: { $first: '$status' },
              wmDispatchQuantity: { $sum: '$wmDispatchQuantity' },
            },
          },
          {
            $lookup: {
              from: 'equipment',
              localField: 'equipment',
              foreignField: '_id',
              as: 'equipment',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    name: 1,
                    equipmentNumber: 1,
                    serialNumber: 1,
                    equipmentImage: 1,
                    equipmentType: 1,
                  },
                },
                {
                  $lookup: {
                    from: 'equipment-types',
                    localField: 'equipmentType',
                    foreignField: '_id',
                    as: 'equipmentType',
                    pipeline: [
                      {
                        $project: {
                          _id: 1,
                          type: 1,
                          isTemporary: { $ifNull: ['$isTemporary', false] },
                        },
                      },
                    ],
                  },
                },
                {
                  $unwind: '$equipmentType',
                },
              ],
            },
          },
          {
            $unwind: '$equipment',
          },
        ],
        as: 'equipmentOrderHistory',
      },
    },
    {
      $unwind: '$equipmentOrderHistory',
    },
    {
      $group: {
        _id: '$_id',
        equipmentDetail: {
          $push: {
            _id: '$equipmentOrderHistory.equipment._id',
            equipment: '$equipmentOrderHistory.equipment.name',
            equipmentImage: '$equipmentOrderHistory.equipment.equipmentImage',
            status: '$equipmentOrderHistory.status',
            wmDispatchQuantity: '$equipmentOrderHistory.wmDispatchQuantity',
            equipmentTypeDetails: {
              _id: '$equipmentOrderHistory.equipment.equipmentType._id',
              type: '$equipmentOrderHistory.equipment.equipmentType.type',
              isTemporary: '$equipmentOrderHistory.equipment.equipmentType.isTemporary',
            },
          },
        },
      },
    },
  ];

  return pmOrderManageEquipment.aggregate(aggregateFunction);
};

/**
 * Check Linked Equipment
 *
 * @param {*} pmOrder
 * @param {*} userId
 * @returns
 */
exports.checkLinkedEquipment = async (pmOrder, userId) => {
  // Fetch PM Order Manage Data
  const getOrderManageData = await pmOrderManageEquipment.find({
    status: {
      $in: ['approved', 'partially-pre-transit', 'partially-in-transit', 'partially-check-in'],
    },
    pmOrder: pmOrder,
    deletedAt: null,
  });

  // Check PM Order Manage Data
  if (getOrderManageData.length === 0) {
    return responseUtils.throwCustomErrorWithStatus(constantUtils.PM_ORDER_DETAILS_NOT_FOUND, 400);
  }

  /*----- Check Linked Equipment ------*/
  let existCounter = 0;
  for (let item of getOrderManageData) {
    if (item.equipment.length === 0) {
      existCounter += 1;
    }
  }

  if (getOrderManageData.length === existCounter) {
    return responseUtils.throwCustomErrorWithStatus(constantUtils.REQUIRED_EQUIPMENT_NOT_LINK, 400);
  }
  /*----- End ------*/

  /*----- Check Linked Equipment Quantity And Update Status ------*/
  let processCount = 0;
  for (let key of getOrderManageData) {
    const getOrderHistory = await EquipmentOrderHistory.find({
      pmOrderManageEquipment: key._id,
      deletedAt: null,
    });

    let wmLinkedQuantity = 0;
    for (let item of getOrderHistory) {
      wmLinkedQuantity += item.wmDispatchQuantity;
    }

    if (wmLinkedQuantity > key.wmDispatchQuantity) {
      let updateManageOrder = await pmOrderManageEquipment.findByIdAndUpdate(key._id, {
        $set: {
          status:
            key.wmApprovedQuantity !== wmLinkedQuantity ? 'partially-pre-transit' : 'pre-transit',
          wmDispatchQuantity: wmLinkedQuantity,
          updatedBy: userId,
          updatedAt: new Date(),
        },
      });
      if (updateManageOrder) {
        processCount += 1;
      }
    } else {
      processCount += 1;
    }
  }

  if (processCount == getOrderManageData.length) {
    return processCount;
  }
  /*----- End ------*/
};

/**
 * Get Warehouse Checkin List
 *
 * @param {*} filter
 * @returns
 */
exports.warehouseCheckinList = async filter => {
  if (filter.status === 'return-rejected') {
    filter.status = 'rejected';
  }
  let pipeline = [
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'equipment',
        localField: 'equipment',
        foreignField: '_id',
        as: 'equipment',
        pipeline: [
          {
            $project: {
              equipmentImage: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$equipment',
    },
    {
      $lookup: {
        from: 'return-orders',
        localField: 'returnOrder',
        foreignField: '_id',
        as: 'order',
        pipeline: [
          ...(filter.status == 'rejected'
            ? [
                {
                  $match: {
                    status: 'rejected',
                  },
                },
              ]
            : []),
        ],
      },
    },
    {
      $unwind: '$order',
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        as: 'project',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'createdBy',
        foreignField: '_id',
        as: 'user',
      },
    },
    {
      $unwind: '$user',
    },
    {
      $unwind: '$project',
    },
    {
      $group: {
        returnOrderId: { $first: '$order._id' },
        _id: {
          order: '$order.orderNumber',
          project: '$project.title',
        },
        equipment: {
          $push: {
            $mergeObjects: ['$equipment', { pmDispatchQuantity: '$pmDispatchQuantity' }],
          },
        },
        returnBy: {
          $first: {
            name: {
              $concat: [
                { $ifNull: ['$user.callingName', '$user.firstName'] },
                ' ',
                '$user.lastName',
              ],
            },
            createdAt: '$createdAt',
          },
        },
      },
    },
    {
      $sort: { 'returnBy.createdAt': -1 },
    },
    {
      $project: {
        _id: 0,
        returnOrderId: 1,
        order: '$_id.order',
        project: '$_id.project',
        equipment: 1,
        returnBy: 1,
      },
    },
  ];

  return await ReturnOrderHistory.aggregate(pipeline);
};

/**
 * Get Warehouse return Details
 *
 * @param {*} filter
 * @returns
 */
exports.warehouseOrderDetails = async (filter, page, perPage, status, sort) => {
  let pipeline;
  let commonStages = [
    {
      $match: filter,
    },
    {
      $sort: {
        createdAt: sort,
      },
    },
    {
      $lookup: {
        from: 'equipment',
        localField: 'equipment',
        foreignField: '_id',
        as: 'equipment',
        pipeline: [
          {
            $lookup: {
              from: 'equipment-types',
              localField: 'equipmentType',
              foreignField: '_id',
              as: 'equipmentType',
              pipeline: [
                {
                  $lookup: {
                    from: 'equipment-units',
                    localField: 'equipmentUnit',
                    foreignField: '_id',
                    as: 'equipmentUnit',
                    pipeline: [
                      {
                        $project: {
                          title: 1,
                          abbreviation: 1,
                        },
                      },
                    ],
                  },
                },
                {
                  $unwind: '$equipmentUnit',
                },
                {
                  $lookup: {
                    from: 'currency-units',
                    localField: 'currencyUnit',
                    foreignField: '_id',
                    as: 'currencyUnit',
                    pipeline: [
                      {
                        $project: {
                          name: 1,
                          symbol: 1,
                        },
                      },
                    ],
                  },
                },
                {
                  $unwind: '$currencyUnit',
                },
                {
                  $lookup: {
                    from: 'equipment-categories',
                    localField: 'equipmentCategory',
                    foreignField: '_id',
                    as: 'equipmentCategory',
                    pipeline: [
                      {
                        $project: {
                          name: 1,
                        },
                      },
                    ],
                  },
                },
                {
                  $unwind: '$equipmentCategory',
                },
                {
                  $lookup: {
                    from: 'equipment-quantity-types',
                    localField: 'quantityType',
                    foreignField: '_id',
                    as: 'quantityType',
                    pipeline: [
                      {
                        $project: {
                          name: 1,
                          quantityType: 1,
                          priceType: 1,
                        },
                      },
                    ],
                  },
                },
                {
                  $unwind: '$quantityType',
                },
                {
                  $lookup: {
                    from: 'hs-codes',
                    localField: 'hsCode',
                    foreignField: '_id',
                    as: 'hsCode',
                    pipeline: [
                      {
                        $project: {
                          code: 1,
                        },
                      },
                    ],
                  },
                },
                {
                  $unwind: '$hsCode',
                },
                {
                  $project: {
                    _id: 0,
                    createdBy: 0,
                    updatedBy: 0,
                    deletedBy: 0,
                    deletedAt: 0,
                    createdAt: 0,
                    updatedAt: 0,
                    __v: 0,
                  },
                },
              ],
            },
          },
          {
            $unwind: '$equipmentType',
          },
          {
            $project: {
              _id: 1,
              createdBy: 0,
              updatedBy: 0,
              deletedBy: 0,
              deletedAt: 0,
              createdAt: 0,
              updatedAt: 0,
              __v: 0,
            },
          },
        ],
      },
    },
    {
      $unwind: '$equipment',
    },
    {
      $lookup: {
        from: 'return-orders',
        localField: 'returnOrder',
        foreignField: '_id',
        as: 'order',
      },
    },
    {
      $unwind: '$order',
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        as: 'project',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'createdBy',
        foreignField: '_id',
        as: 'user',
      },
    },
    {
      $unwind: '$user',
    },
    {
      $unwind: '$project',
    },
    {
      $group: {
        _id: {
          order: '$order.orderNumber',
          project: '$project.title',
        },
        equipment: {
          $push: {
            $mergeObjects: [
              '$equipment',
              { pmDispatchQuantity: '$pmDispatchQuantity' },
              { wmComment: '$wmComment' },
              { status: '$status' },
            ],
          },
        },
        returnBy: {
          $first: {
            name: {
              $concat: [
                { $ifNull: ['$user.callingName', '$user.firstName'] },
                ' ',
                '$user.lastName',
              ],
            },
            createdAt: '$createdAt',
            profileImage: '$user.profileImage',
          },
        },
        totalPmDispatchQuantity: { $sum: '$pmDispatchQuantity' },
        // remark: {
        //   $push: {
        //     wmComment: { $ifNull: ['$equipment.wmComment', []] },
        //     pmRemark: '$order.pmRemark',
        //   },
        // },
        returnOrderId: { $first: '$order._id' },
        status: {
          $first: '$order.status',
        },
        createdAt: {
          $first: '$createdAt',
        },
      },
    },
    {
      $addFields: {
        equipment: {
          $slice: [
            '$equipment',
            parseInt(page) * parseInt(perPage) - parseInt(perPage),
            parseInt(perPage),
          ],
        },
      },
    },

    {
      $project: {
        _id: 0,
        returnOrderId: 1,
        order: '$_id.order',
        status: 1,
        project: '$_id.project',
        equipment: 1,
        returnBy: 1,
        totalPmDispatchQuantity: 1,
        pmRemark: 1,
        wmRemark: 1,
        remark: 1,
        createdAt: 1,
      },
    },
  ];

  if (status == 'return') {
    pipeline = [
      ...commonStages,
      {
        $addFields: {
          equipment: {
            $filter: {
              input: '$equipment',
              as: 'equip',
              cond: { $eq: ['$$equip.status', 'return'] },
            },
          },
        },
      },
    ];
  } else {
    pipeline = commonStages;
  }

  return await ReturnOrderHistory.aggregate(pipeline);
};

/**
 * Wm Reject Missing Equipment
 *
 * @param {*} id
 * @param {*} data
 * @returns
 */
exports.wmRejectMissingEquipment = async (filter, data) => {
  return ReturnOrderHistory.findByOneAndUpdate(filter, data, { new: true });
};

exports.orderPDFDetails = async filter => {
  let pipeline = [
    {
      $match: filter,
    },
    aggregateComponentUtils.aggregateLookup('project'),
    aggregateComponentUtils.aggregateUnwind('project'),
    aggregateComponentUtils.aggregateLookup('createdBy'),
    aggregateComponentUtils.aggregateUnwind('createdBy'),
    aggregateComponentUtils.aggregateLookup('updatedBy'),
    aggregateComponentUtils.aggregateUnwind('updatedBy'),
    aggregateComponentUtils.aggregateLookup('account'),
    aggregateComponentUtils.aggregateUnwind('account'),
    {
      $lookup: {
        from: 'pm-order-manage-equipments',
        localField: '_id',
        foreignField: 'pmOrder',
        as: 'pmManageEquipmentArray',
      },
    },
    {
      $lookup: {
        from: 'pm-order-manage-equipments',
        localField: '_id',
        foreignField: 'pmOrder',
        as: 'pmManageEquipments',
      },
    },
    {
      $unwind: '$pmManageEquipments',
    },
    {
      $lookup: {
        from: 'equipment-order-histories',
        localField: 'pmManageEquipments._id',
        foreignField: 'pmOrderManageEquipment',
        pipeline: [
          { $project: { _id: 1, equipment: 1, wmDispatchQuantity: 1 } },
          {
            $lookup: {
              from: 'equipment',
              localField: 'equipment',
              foreignField: '_id',
              pipeline: [
                {
                  $project: {
                    name: 1,
                    equipmentType: 1,
                    serialNumber: 1,
                    hsCode: 1,
                    weight: 1,
                    value: 1,
                  },
                },
                {
                  $lookup: {
                    from: 'equipment-types',
                    localField: 'equipmentType',
                    foreignField: '_id',
                    pipeline: [
                      { $project: { type: 1, hsCode: 1, currencyUnit: 1, equipmentUnit: 1 } },
                      {
                        $lookup: {
                          from: 'hs-codes',
                          localField: 'hsCode',
                          foreignField: '_id',
                          pipeline: [{ $project: { code: 1 } }],
                          as: 'hsCode',
                        },
                      },
                      {
                        $unwind: '$hsCode',
                      },
                      {
                        $lookup: {
                          from: 'currency-units',
                          localField: 'currencyUnit',
                          foreignField: '_id',
                          pipeline: [{ $project: { symbol: 1 } }],
                          as: 'currencyUnit',
                        },
                      },
                      {
                        $unwind: '$currencyUnit',
                      },
                      {
                        $lookup: {
                          from: 'equipment-units',
                          localField: 'equipmentUnit',
                          foreignField: '_id',
                          pipeline: [{ $project: { abbreviation: 1 } }],
                          as: 'equipmentUnit',
                        },
                      },
                      {
                        $unwind: '$equipmentUnit',
                      },
                    ],
                    as: 'equipmentType',
                  },
                },
                {
                  $unwind: '$equipmentType',
                },
              ],
              as: 'equipment',
            },
          },
          {
            $unwind: '$equipment',
          },
        ],
        as: 'equipmentOrderHistories',
      },
    },
    {
      $unwind: '$equipmentOrderHistories',
    },
    {
      $addFields: {
        totalQuantityValue: {
          $multiply: [
            '$equipmentOrderHistories.wmDispatchQuantity',
            '$equipmentOrderHistories.equipment.value',
          ],
        },
      },
    },
    {
      $group: {
        _id: '$_id',
        orderNumber: { $first: '$orderNumber' },
        requestedQuantity: {
          $first: {
            $sum: '$pmManageEquipmentArray.pmRequestedQuantity',
          },
        },
        requestedItems: {
          $first: {
            $size: '$pmManageEquipmentArray',
          },
        },
        project: { $first: '$project' },
        account: { $first: '$account' },
        status: { $first: '$status' },
        fromDate: { $first: '$fromDate' },
        toDate: { $first: '$toDate' },
        orderEquipment: {
          $push: {
            quantity: '$equipmentOrderHistories.wmDispatchQuantity',
            equipmentType: '$equipmentOrderHistories.equipment.equipmentType.type',
            equipmentName: '$equipmentOrderHistories.equipment.name',
            serialNumber: '$equipmentOrderHistories.equipment.serialNumber',
            hsCode: '$equipmentOrderHistories.equipment.equipmentType.hsCode.code',
            currencyUnit: '$equipmentOrderHistories.equipment.equipmentType.currencyUnit.symbol',
            currencyUnitId: '$equipmentOrderHistories.equipment.equipmentType.currencyUnit._id',
            totalQuantityValue: '$totalQuantityValue',
            weight: {
              $concat: [
                { $toString: '$equipmentOrderHistories.equipment.weight' },
                ' ',
                '$equipmentOrderHistories.equipment.equipmentType.equipmentUnit.abbreviation',
              ],
            },
            value: {
              $concat: [
                '$equipmentOrderHistories.equipment.equipmentType.currencyUnit.symbol',
                { $toString: '$equipmentOrderHistories.equipment.value' },
              ],
            },
            weightValue: '$equipmentOrderHistories.equipment.weight',
            unitValue: '$equipmentOrderHistories.equipment.equipmentUnit.abbreviation',
          },
        },
        totalWeight: { $sum: '$equipmentOrderHistories.equipment.weight' },
        totalValue: { $sum: '$totalQuantityValue' },
        currencyUnit: {
          $first: '$equipmentOrderHistories.equipment.equipmentType.currencyUnit.symbol',
        },
        createdBy: { $first: '$createdBy' },
        createdAt: { $first: '$createdAt' },
        updatedBy: { $first: '$updatedBy' },
        updatedAt: { $first: '$updatedAt' },
      },
    },
  ];

  return await PmOrder.aggregate(pipeline);
};

/**
 * Get PM Orders count
 *
 * @param {*} filter
 * @param {*} status
 * @returns
 */
exports.getPMOrderListCount = async (filterData, status) => {
  let aggregateFunction = [
    {
      $match: filterData,
    },
    {
      $lookup: {
        from: 'pm-order-manage-equipments',
        localField: '_id',
        foreignField: 'pmOrder',
        pipeline: [
          {
            $match: {
              status: status,
              deletedAt: null,
            },
          },
        ],
        as: 'pmManageEquipment',
      },
    },
    {
      $unwind: '$pmManageEquipment',
    },
    {
      $group: {
        _id: '$_id',
        orderNumber: {
          $first: '$orderNumber',
        },
      },
    },
  ];
  return await PmOrder.aggregate(aggregateFunction);
};
