const ReturnOrder = require('../models/return-order.model');
const EquipmentOrderHistory = require('../models/equipment-order-history.model');

exports.getOrderEquipmentList = async (filter, page, perPage, sort, otherFilter = null) => {
  let aggregateFunction = [
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'accounts',
        localField: 'account',
        foreignField: '_id',
        pipeline: [{ $project: { _id: 1, name: 1 } }],
        as: 'account',
      },
    },
    {
      $unwind: '$account',
    },
    {
      $lookup: {
        from: 'users',
        localField: 'createdBy',
        foreignField: '_id',
        pipeline: [{ $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } }],
        as: 'createdBy',
      },
    },
    {
      $unwind: '$createdBy',
    },
    {
      $lookup: {
        from: 'equipment',
        localField: 'equipment',
        foreignField: '_id',
        as: 'equipmentDetails',
        pipeline: [
          {
            $project: {
              _id: 1,
              name: 1,
              equipmentNumber: 1,
              qrCode: 1,
              equipmentImage: 1,
              serialNumber: 1,
              certificateType: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: '$equipmentDetails',
    },
    {
      $lookup: {
        from: 'equipment-types',
        localField: 'equipmentType',
        foreignField: '_id',
        as: 'equipmentTypeDetails',
        pipeline: [{ $project: { _id: 1, type: 1, equipmentCategory: 1, quantityType: 1 } }],
      },
    },
    {
      $unwind: '$equipmentTypeDetails',
    },
    {
      $lookup: {
        from: 'equipment-categories',
        localField: 'equipmentTypeDetails.equipmentCategory',
        foreignField: '_id',
        as: 'equipmentCategoryDetails',
        pipeline: [{ $project: { _id: 1, name: 1 } }],
      },
    },
    {
      $unwind: '$equipmentCategoryDetails',
    },
    {
      $lookup: {
        from: 'equipment-quantity-types',
        localField: 'equipmentTypeDetails.quantityType',
        foreignField: '_id',
        as: 'quantityTypeDetails',
        pipeline: [{ $project: { _id: 1, name: 1, priceType: 1, quantityType: 1 } }],
      },
    },
    {
      $unwind: '$quantityTypeDetails',
    },
    {
      $lookup: {
        from: 'pm-orders',
        localField: 'pmOrder',
        foreignField: '_id',
        as: 'pmOrderDetails',
        pipeline: [{ $project: { _id: 1, orderNumber: 1, status: 1, project: 1 } }],
      },
    },
    {
      $unwind: '$pmOrderDetails',
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'pmOrderDetails.project',
        foreignField: '_id',
        as: 'projectDetails',
      },
    },
    {
      $unwind: '$projectDetails',
    },
    {
      $group: {
        _id: {
          projectId: '$projectDetails._id',
          equipmentId: '$equipment',
          returnStatus: '$returnStatus',
        },
        equipmentName: { $first: '$equipmentDetails.name' },
        equipmentNumber: { $first: '$equipmentDetails.equipmentNumber' },
        equipmentImage: { $first: '$equipmentDetails.equipmentImage' },
        qrCode: { $first: '$equipmentDetails.qrCode' },
        equipmentCertificate: { $first: '$equipmentDetails.certificateType' },
        serialNumber: { $first: '$equipmentDetails.serialNumber' },
        equipmentType: { $first: '$equipmentTypeDetails.type' },
        equipmentCategory: { $first: '$equipmentCategoryDetails.name' },
        equipmentQuantityType: { $first: '$quantityTypeDetails.quantityType' },
        equipmentPriceType: { $first: '$quantityTypeDetails.priceType' },
        pmReceivedQuantity: { $sum: '$pmReceivedQuantity' },
        pmDispatchQuantity: { $first: '$pmDispatchQuantity' },
        status: { $first: '$status' },
        returnStatus: { $first: '$returnStatus' },
        returnOrder: { $first: '$returnOrder' },
        orderNumber: { $push: '$pmOrderDetails.orderNumber' },
        orderBy: {
          $first: {
            id: '$createdBy._id',
            callingName: '$createdBy.callingName',
            firstName: '$createdBy.firstName',
            lastName: '$createdBy.lastName',
          },
        },
        equipmentHistoryIds: { $push: '$_id' },
        project: { $first: '$projectDetails.title' },
        account: { $first: '$account' },
        createdAt: { $first: '$createdAt' },
        updatedAt: { $first: '$updatedAt' },
      },
    },
    {
      $sort: {
        createdAt: sort,
      },
    },
  ];

  if (otherFilter) {
    aggregateFunction.push({
      $match: otherFilter,
    });
  }

  if (page !== null && perPage !== null) {
    aggregateFunction.push(
      {
        $skip: parseInt(page) * parseInt(perPage),
      },
      {
        $limit: parseInt(perPage),
      }
    );
  }
  return await EquipmentOrderHistory.aggregate(aggregateFunction);
};

exports.createReturnOrder = async (requestData, session) => {
  const returnOrder = new ReturnOrder(requestData);
  return await returnOrder.save({ session });
};

exports.updateReturnOrder = async (id, requestData, session) => {
  return await ReturnOrder.findByIdAndUpdate(id, requestData, {
    new: true,
    session,
  });
};

exports.removeReturnOrder = async (id, session) => {
  return await ReturnOrder.findByIdAndDelete(id, { session });
};

exports.getSingleReturnOrderDataByFilter = async filter => {
  return await ReturnOrder.findOne(filter);
};

/**
 * Update Return Order
 *
 * @param {*} filter
 * @returns
 */
exports.updateReturn = async (filter, data, session) => {
  return await ReturnOrder.findOneAndUpdate(filter, data, { new: true, session });
};

exports.pmReturnOrderList = async (filter, search, page, perPage, sort) => {
  let aggregateFunction = [
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        pipeline: [{ $project: { _id: 1, title: 1 } }],
        as: 'project',
      },
    },
    {
      $unwind: '$project',
    },
    {
      $lookup: {
        from: 'accounts',
        localField: 'account',
        foreignField: '_id',
        pipeline: [{ $project: { _id: 1, name: 1 } }],
        as: 'account',
      },
    },
    {
      $unwind: '$account',
    },
    {
      $lookup: {
        from: 'users',
        localField: 'createdBy',
        foreignField: '_id',
        pipeline: [{ $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } }],
        as: 'createdBy',
      },
    },
    {
      $unwind: '$createdBy',
    },
    {
      $match: {
        $or: [
          {
            orderNumber: search ? { $regex: search, $options: 'i' } : { $exists: true },
          },
          {
            'createdBy.firstName': search ? { $regex: search, $options: 'i' } : { $exists: true },
          },
          {
            'createdBy.lastName': search ? { $regex: search, $options: 'i' } : { $exists: true },
          },
          {
            'createdBy.callingName': search ? { $regex: search, $options: 'i' } : { $exists: true },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'return-order-histories',
        localField: '_id',
        foreignField: 'returnOrder',
        pipeline: [
          {
            $project: {
              _id: 1,
              equipment: 1,
              status: 1,
              pmDispatchQuantity: 1,
              wmReceivedQuantity: 1,
              wmComment: 1,
            },
          },
          {
            $lookup: {
              from: 'equipment',
              localField: 'equipment',
              foreignField: '_id',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    name: 1,
                    equipmentNumber: 1,
                    serialNumber: 1,
                    equipmentImage: 1,
                    qrCode: 1,
                    equipmentType: 1,
                  },
                },
                {
                  $lookup: {
                    from: 'equipment-types',
                    localField: 'equipmentType',
                    foreignField: '_id',
                    pipeline: [
                      { $project: { _id: 1, type: 1, equipmentCategory: 1 } },
                      {
                        $lookup: {
                          from: 'equipment-categories',
                          localField: 'equipmentCategory',
                          foreignField: '_id',
                          pipeline: [{ $project: { _id: 1, name: 1 } }],
                          as: 'equipmentCategory',
                        },
                      },
                      {
                        $unwind: '$equipmentCategory',
                      },
                    ],
                    as: 'equipmentType',
                  },
                },
                {
                  $unwind: '$equipmentType',
                },
              ],
              as: 'equipment',
            },
          },
          {
            $unwind: '$equipment',
          },
        ],
        as: 'returnOrderHistory',
      },
    },
    {
      $unwind: '$returnOrderHistory',
    },
    {
      $lookup: {
        from: 'return-order-histories',
        localField: '_id',
        foreignField: 'returnOrder',
        as: 'returnOrderHistoryArray',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'pmRemark.user',
        foreignField: '_id',
        as: 'pmRemarkUsers',
        pipeline: [{ $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } }],
      },
    },
    {
      $addFields: {
        pmRemark: {
          $map: {
            input: '$pmRemark',
            as: 'remark',
            in: {
              $mergeObjects: [
                '$$remark',
                {
                  user: {
                    $arrayElemAt: [
                      '$pmRemarkUsers',
                      { $indexOfArray: ['$pmRemarkUsers._id', '$$remark.user'] },
                    ],
                  },
                },
              ],
            },
          },
        },
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'wmRemark.user',
        foreignField: '_id',
        as: 'wmRemarkUsers',
        pipeline: [{ $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1 } }],
      },
    },
    {
      $addFields: {
        wmRemark: {
          $map: {
            input: '$wmRemark',
            as: 'remark',
            in: {
              $mergeObjects: [
                '$$remark',
                {
                  user: {
                    $arrayElemAt: [
                      '$wmRemarkUsers',
                      { $indexOfArray: ['$wmRemarkUsers._id', '$$remark.user'] },
                    ],
                  },
                },
              ],
            },
          },
        },
      },
    },
    {
      $group: {
        _id: '$_id',
        orderNumber: { $first: '$orderNumber' },
        pmRemark: { $first: '$pmRemark' },
        wmRemark: { $first: '$wmRemark' },
        orderBy: {
          $first: {
            id: '$createdBy._id',
            callingName: '$createdBy.callingName',
            firstName: '$createdBy.firstName',
            lastName: '$createdBy.lastName',
          },
        },
        returnOrderHistory: {
          $push: '$returnOrderHistory',
        },
        totalReturnedQuantity: {
          $sum: '$returnOrderHistory.pmDispatchQuantity',
        },
        totalItems: {
          $first: {
            $size: '$returnOrderHistoryArray',
          },
        },
        project: { $first: '$project' },
        account: { $first: '$account' },
        createdAt: { $first: '$createdAt' },
        updatedAt: { $first: '$updatedAt' },
      },
    },
    {
      $sort: {
        createdAt: sort,
      },
    },
    {
      $group: {
        _id: '$project._id',
        project: { $first: '$project' },
        returnOrderList: {
          $push: {
            _id: '$_id',
            orderNumber: '$orderNumber',
            remark: {
              wmRemark: '$wmRemark',
              pmRemark: '$pmRemark',
            },
            orderBy: '$orderBy',
            returnOrderHistory: '$returnOrderHistory',
            totalReturnedQuantity: '$totalReturnedQuantity',
            totalItems: '$totalItems',
            createdAt: '$createdAt',
            updatedAt: '$updatedAt',
          },
        },
        account: { $first: '$account' },
      },
    },
  ];

  aggregateFunction.push(
    {
      $skip: parseInt(page) * parseInt(perPage),
    },
    {
      $limit: parseInt(perPage),
    }
  );

  return await ReturnOrder.aggregate(aggregateFunction);
};

/**
 * get project return order list
 *
 * @param {*} filter
 * @param {*} page
 * @param {*} perPage
 * @param {*} sort
 * @returns
 */
exports.getProjectReturnOrderList = async (filter, page, perPage, sort, search) => {
  let aggregateFunction = [
    {
      $match: filter,
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        foreignField: '_id',
        pipeline: [{ $project: { _id: 1, title: 1 } }],
        as: 'project',
      },
    },
    {
      $unwind: '$project',
    },
    {
      $match: {
        'project.title': search ? { $regex: search, $options: 'i' } : { $exists: true },
      },
    },
    {
      $group: {
        _id: '$project._id',
        project: { $first: '$project' },
        totalReturnOrders: { $sum: 1 },
        createdAt: { $max: '$createdAt' },
        updatedAt: { $max: '$updatedAt' },
      },
    },
    {
      $sort: {
        createdAt: sort,
      },
    },
  ];
  aggregateFunction.push(
    {
      $skip: parseInt(page) * parseInt(perPage),
    },
    {
      $limit: parseInt(perPage),
    }
  );
  return await ReturnOrder.aggregate(aggregateFunction);
};
