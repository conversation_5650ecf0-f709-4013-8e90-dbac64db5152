const { toObjectId } = require('../utils/common.utils');
const Activity = require('../models/activity.model');

/**
 * Create Activity
 *
 * @param {*} Activity
 * @returns
 */
exports.createActivity = async requestData => await Activity.create(requestData);

/**
 * Get All Activity
 *
 * @returns
 */
exports.getAllActivity = async filter => {
  return Activity.find(filter)
    .populate([
      {
        path: 'scopeId',
        model: 'scope',
        select: 'name',
      },
      {
        path: 'project',
        select: 'title',
      },
      {
        path: 'account',
        select: 'name',
      },
    ])
    .sort({ sortOrder: 1 });
};

/**
 * Update Activity
 *
 * @param {*} id
 * @param {*} update
 * @returns
 */
exports.updateActivity = async (id, update) =>
  Activity.findByIdAndUpdate(id, update, { new: true }).populate([
    {
      path: 'scopeId',
      model: 'scope',
      select: 'name',
    },
    {
      path: 'project',
      select: 'title',
    },
    {
      path: 'account',
      select: 'name',
    },
  ]);

/**
 * Delete Activity
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteActivity = async (id, deletedAt) =>
  Activity.findByIdAndUpdate(id, { $set: deletedAt });

/**
 * Get Scope By Id
 *
 * @param {*} id
 * @returns
 */
exports.getActivityById = async id =>
  Activity.find({ $and: [{ _id: toObjectId(id) }, { deletedAt: null }] });

/**
 * Get Activity By projectId
 *
 * @param {*} projectId
 * @returns
 */
exports.getActivityByProjectId = async (
  filter = {},
  page = null,
  perPage = null,
  sort = { sortOrder: 1 }
) => {
  return await Activity.find(filter)
    .collation({ locale: 'en', strength: 2 })
    .sort(sort)
    .limit(perPage)
    .skip(page * perPage)
    .populate([
      {
        path: 'scopeId',
        model: 'scope',
        select: 'name',
      },
      {
        path: 'project',
        select: 'title',
      },
      {
        path: 'account',
        select: 'name',
      },
    ]);
};

/**
 * Get Activity by project id and name
 *
 * @param {*} projectId
 * @param {*} name
 * @returns
 */
exports.getActivityByProjectIdAndName = async filter => Activity.findOne(filter);

/**
 * Delete All Project Activity
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteAllProjectActivity = async (projectId, deletedAt) => {
  return Activity.updateMany(
    { project: projectId },
    {
      $set: deletedAt,
    }
  );
};
