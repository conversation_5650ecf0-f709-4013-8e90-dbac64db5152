const ProjectEquipmentType = require('../models/project-equipment-type.model');

/**
 * Create Project Equipment Types
 *
 * @param {*} requestData
 * @returns
 */
exports.createProjectEquipmentTypes = async requestData => {
  return await ProjectEquipmentType.create(requestData);
};

/**
 * Get Project Equipment Types
 *
 * @param {*} filter
 * @returns
 */
exports.getProjectEquipmentTypes = async filter => {
  return await ProjectEquipmentType.findOne(filter);
};

/**
 * Remove Project Equipment Types
 *
 * @param {*} filter
 * @returns
 */
exports.removeProjectEquipmentTypes = async filter => {
  return await ProjectEquipmentType.findOneAndDelete(filter);
};

/**
 * Project Equipment Type List
 *
 * @param {*} filter
 * @returns
 */
exports.projectEquipmentTypeList = async (filter, sort = 'desc') => {
  return await ProjectEquipmentType.find(filter)
    .sort({ sortOrder: sort })
    .populate([
      {
        path: 'project',
        select: { title: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'account',
        select: { name: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'equipmentType',
        select: { type: 1, _id: 1 },
        strictPopulate: false,
      },
    ]);
};

/**
 * Update Project Equipment Type
 *
 * @param {*} id
 * @param {*} requestData
 * @returns
 */
exports.updateProjectEquipmentType = async (id, requestData) => {
  return await ProjectEquipmentType.findByIdAndUpdate(id, requestData, { new: true });
};

/**
 * Get Project Equipment Type by Id
 *
 * @param {*} id
 * @param {*}
 * @returns
 */

exports.getProjectEquipmentTypeById = async projectEquipmentTypeId => {
  return await ProjectEquipmentType.findById(projectEquipmentTypeId);
};

exports.getProjectEquipmentTypeByFilter = async filter => {
  return await ProjectEquipmentType.find(filter);
};
