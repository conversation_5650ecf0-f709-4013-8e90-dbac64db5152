const Team = require('../models/team.model');

/**
 * Create Team
 *
 * @param {*} team
 * @returns
 */
exports.createTeam = async team => {
  return await Team.create(team);
};

/**
 * Update By Id
 *
 * @param {*} id
 * @param {*} team
 * @returns
 */
exports.updateTeam = async (id, team) => {
  return Team.findByIdAndUpdate(id, { $set: team }, { new: true }).populate([
    {
      path: 'project',
      select: 'title',
    },
    {
      path: 'account',
      select: 'name',
    },
  ]);
};

/**
 * Delete By Id
 *
 * @param {*} id
 * @returns
 */
exports.deleteTeam = async (id, deletedAt) => {
  return Team.findByIdAndUpdate(id, { $set: deletedAt }, { new: true });
};

/**
 * Get All Team
 *
 * @param {*} page
 * @param {*} perPage
 * @returns
 */
exports.getAllTeam = async filterData => {
  return Team.find(
    {
      $and: [{ account: filterData.account }, { deletedAt: filterData.deletedAt }],
    },
    { createdAt: 0, updatedAt: 0, __v: 0 }
  );
};

/**
 * Get By Id
 *
 * @param {*} id
 * @returns
 */
exports.getTeamById = async id => {
  return Team.findOne(
    {
      $and: [{ _id: id }, { deletedAt: null }],
    },
    { createdAt: 0, updatedAt: 0, __v: 0 }
  ).populate([
    {
      path: 'project',
      select: 'title',
    },
    {
      path: 'account',
      select: 'name',
    },
  ]);
};

/**
 * Get Team By Name
 *
 * @param {*} teamName
 * @returns
 */
exports.getTeamByName = async (account, projectId, teamName, isDefault) => {
  return Team.findOne({
    $and: [
      { account: account },
      { project: projectId },
      { teamsWfmName: teamName },
      { isDefault: isDefault },
      { deletedAt: null },
    ],
  });
};

/**
 * Delete All Project Team
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteAllProjectTeam = async (projectId, deletedAt) => {
  return Team.updateMany(
    { project: projectId },
    {
      $set: deletedAt,
    }
  );
};

/**
 * Get Team By project id
 *
 * @param {*} projectId
 * @returns
 */
exports.getTeamByProjectId = async (
  filter = {},
  page = null,
  perPage = null,
  sort = { createdAt: -1 }
) => {
  return Team.find(filter)
    .collation({ locale: 'en', strength: 2 })
    .sort(sort)
    .limit(perPage)
    .skip(page * perPage)
    .populate([
      {
        path: 'project',
        select: 'title',
      },
      {
        path: 'account',
        select: 'name',
      },
    ]);
};
