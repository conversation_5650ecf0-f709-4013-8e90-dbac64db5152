const Dpr = require('../models/dpr.model');

/**
 * Create DPR
 *
 * @param {*} requestData
 * @returns
 */
exports.createDpr = async requestData => {
  return await Dpr.create(requestData);
};

/**
 * Update DPR
 *
 * @param {*} requestData
 * @returns
 */
exports.updateDpr = async (filter, update) => {
  return await Dpr.updateOne(filter, update);
};

/**
 * Update DPR with dpr id
 *
 * @param {String} dprId - DPR ID
 * @param {Object} data - Updated data
 * @returns {Object} - Updated DPR data
 */
exports.updateDprReloadStatus = async (dprId, data) => {
  return await Dpr.findByIdAndUpdate(dprId, { $set: data }, { new: true });
};

/**
 * Delete DPR
 *
 * @param {*} id
 * @returns
 */
exports.deleteDpr = async (id, deletedAt) => {
  return await Dpr.findByIdAndUpdate(id, { $set: deletedAt }, { new: true });
};

/**
 * Hard Delete DPR
 *
 * @param {String} id - DPR ID
 * @returns {Object} - Deleted DPR data
 */
exports.hardDeleteDpr = async dprId => {
  return await Dpr.findByIdAndDelete(dprId);
};

/**
 * Get All DPR
 *
 * @param {*} filter
 * @returns
 */
exports.getAllDpr = async filter => {
  return Dpr.find(filter).sort({ createdAt: -1 });
};

/**
 * Get All DPRs with Filters, Pagination
 *
 * @param {Object} filter - Filter criteria
 * @param {Number} page - Current page number
 * @param {Number} perPage - Number of records per page
 * @returns {Object} - Paginated DPR data
 */
exports.getListOfAllDprs = async (filter, page, perPage, sortBy, sortOrder) => {
  const query = Dpr.find(filter)
    .sort({ [sortBy]: sortOrder })
    .populate([
      { path: 'project', select: 'title' },
      { path: 'account', select: 'name' },
      { path: 'createdBy', model: 'user', select: { firstName: 1, lastName: 1, callingName: 1 } },
    ])
    .select({
      _id: 1,
      version: 1,
      prevVersion: 1,
      dprNo: 1,
      project: 1,
      dprDate: 1,
      status: 1,
      createdBy: 1,
      createdAt: 1,
    });

  query.skip(page * perPage).limit(perPage);

  // eslint-disable-next-line no-undef
  const [dprs, total] = await Promise.all([query.exec(), Dpr.countDocuments(filter)]);

  return { dprs, total };
};

/**
 * Get DPR Members
 *
 * @param {*} filter
 * @returns
 */
exports.getLatestDpr = async filter => {
  return Dpr.findOne(filter);
};

/**
 * Get DPR Details
 *
 * @param {*} filter
 * @returns
 */
exports.getDprDetails = async filter => {
  return await Dpr.findOne(filter);
};

/**
 * Get DPR data by dprVersion
 *
 * @param {String} dprVersion - DPR version
 * @returns {Object} - DPR data
 */
exports.getDprDataByDprVersion = (data, dprVersion) => {
  const dprData = data.find(dpr => dpr.version === dprVersion);
  return dprData;
};
