/*models*/
const Type = require('../models/type.model');

/**
 * Define the replacement mapping
 * Keys are the old titles, values are the new titles
 */
const replacementMap = {
  'Near-miss': 'Near Miss',
  'Minor Injury': 'Occupational Injury',
  'Major Injury': 'Occupational Health / Illness',
  Fatal: 'Property Damage',
  Environment: 'Property Breakdown / Malfunction',
};

/**
 * Define the final desired state of types
 */
const desiredTitles = [
  { title: 'Near Miss', color: 'CBD7E5' },
  { title: 'Occupational Injury', color: 'F6CDCE' },
  { title: 'Occupational Health / Illness', color: 'F7BB80' },
  { title: 'Property Damage', color: 'ADD4D1' },
  { title: 'Property Breakdown / Malfunction', color: '9BC795' },
  { title: 'Property Loss / Missing', color: 'F4DF91' },
  { title: 'Environmental - Spill/Leak', color: 'D0AFC7' },
  { title: 'Environmental - Water Pollution', color: 'B6D7D3' },
  { title: 'Environmental - Air Emission', color: 'BAE3B1' },
  { title: 'Security - Breach of Law/Contract', color: 'FFC4CA' },
  { title: 'Others - Non-Work Related Event', color: 'C4AC9F' },
];

/**
 * Prepare and update the types data in collection
 *
 * @returns
 */
exports.up = async () => {
  console.log('Starting type collection update...');

  // Track changes for reporting
  const changes = {
    updated: 0,
    added: 0,
  };

  // Get current state of the database
  const existingTypes = await Type.find({});
  console.log(`Found ${existingTypes.length} existing types in the db`);

  // Process replacements - update existing records based on replacement map
  for (const existingType of existingTypes) {
    const oldTitle = existingType.title;

    // Check if this title needs to be replaced
    if (replacementMap[oldTitle]) {
      const newTitle = replacementMap[oldTitle];

      // Find the other data for the new title from desiredTitles
      const newTypeInfo = desiredTitles.find(type => type.title === newTitle);

      if (newTypeInfo) {
        await Type.updateOne(
          { _id: existingType._id },
          { $set: { title: newTitle, color: newTypeInfo.color } }
        );

        changes.updated++;
        console.log(
          `Updated record with ID ${existingType._id} from "${oldTitle}" to "${newTitle}"`
        );
      }
    }
  }

  // Add any new types from desiredTitles that don't exist yet
  const updatedTypes = await Type.find({});
  const existingTitles = updatedTypes.map(type => type.title);

  for (const desired of desiredTitles) {
    // Skip if this title already exists
    if (!existingTitles.includes(desired.title)) {
      const newType = await Type.create({
        title: desired.title,
        color: desired.color,
      });

      changes.added++;
      console.log(`Added new type: "${desired.title}" with ID ${newType._id}`);
    }
  }

  // Report summary
  console.log(
    `Type collection update completed: ${changes.updated} records updated, ${changes.added} new types added.`
  );

  return changes;
};
