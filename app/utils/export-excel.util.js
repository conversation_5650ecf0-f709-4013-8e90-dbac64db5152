const ExcelJS = require('exceljs');

// service
const { getUserProfileFunction } = require('../services/user.service');
const { getSingleRecord } = require('../services/contractual-detail.service');
const certificateTypeService = require('../services/certificate-type.service');
const { getKinByUserId } = require('../services/next-of-kin.service');

// utils
const commonUtils = require('../utils/common.utils');
const {
  handleCellBackground,
  handleCompletionCellColor,
  formatDate,
  formatDateByTimeZone,
  trainingMatrixCommonData,
  colorCell,
  checkCertificateExpiry,
  checkrequiredCertificate,
  changePersonalDetailsFormat,
  checkInventoryExpiry,
  normalizeCompletion,
} = require('../utils/common-function.utils');
const { CHECKED, CLOSED } = require('./constants.utils');
const responseUtils = require('./response.utils');

/**
 * Export Excel
 *
 * @param {*} res
 * @param {*} fileName
 * @param {*} columns
 * @param {*} rows
 * @param {*} type
 * @returns
 */
exports.exportExcel = async (res, fileName, columns, rows, type = null) => {
  try {
    const workBook = new ExcelJS.Workbook();
    const workSheet = workBook.addWorksheet(fileName);

    // add columns
    workSheet.columns = columns;

    // add rows
    Object.keys(rows).forEach(key => {
      workSheet.addRow(rows[key]);
    });

    if (type === 'shifts') {
      workSheet.getRow(1).eachCell(cell => {
        cell.font = { bold: true };
      });

      // Apply bold borders to all rows
      workSheet.eachRow(row => {
        row.eachCell(cell => {
          cell.border = commonUtils.borderStyle; // Apply bold borders to every cell
        });
      });

      const hoursColumn = 'hours';

      // Apply Excel-compatible format to the hours column only
      workSheet.eachRow((row, rowNumber) => {
        if (rowNumber > 1) {
          const hoursColIndex = columns.findIndex(col => col.key === hoursColumn);
          if (hoursColIndex !== -1) {
            const cell = row.getCell(hoursColIndex + 1);
            if (cell.value) {
              // Handle the hours object returned by formatHoursAndMinutes
              if (typeof cell.value === 'object' && cell.value.excelTimeValue !== undefined) {
                cell.value = cell.value.excelTimeValue;
              } else if (typeof cell.value === 'string' && cell.value.includes(':')) {
                try {
                  const [hours, minutes] = cell.value.split(':').map(Number);

                  if (!isNaN(hours) && !isNaN(minutes)) {
                    const excelTime =
                      hours / global.constant.EXCEL_TIME_DIVISOR.HOURS +
                      minutes / global.constant.EXCEL_TIME_DIVISOR.MINUTES;
                    cell.value = excelTime;
                  }
                } catch (error) {
                  console.log('Error parsing time:', cell.value, error.message);
                }
              }

              // Set the number format for hours cells
              cell.numFmt = '[h]:mm';
            }
          }
        }
      });
    }

    // generate XLS file and send it as response
    await this.sendXLSXResponse(res, workBook, fileName);
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};
/**
 * Generate project tracker excel
 *
 * @param {*} data
 * @param {*} res
 * @param {*} fileName
 * @returns
 */
exports.generateProjectTrackerExcel = async (data, res, fileName) => {
  try {
    const workBook = new ExcelJS.Workbook();
    const workSheet = workBook.addWorksheet(fileName);

    const currentColumn = await this.projectTrackerHeaderData(workSheet, data.scopeData);

    const currentRow = await this.projectTrackerReportData(workSheet, data.projectTrackerData);

    await this.projectTrackerFooterData(
      workSheet,
      data.scopeData,
      data.totalCompletions,
      currentRow,
      currentColumn,
      data.projectData
    );

    for (let row = 1; row <= currentRow + 1; row++) {
      for (let col = 1; col <= currentColumn; col++) {
        workSheet.getCell(row, col).alignment = { vertical: 'middle', horizontal: 'center' };
      }
    }
    // generate XLS file and send it as response
    await this.sendXLSXResponse(res, workBook, fileName);
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Project tracker Header data
 *
 * @param {*} workSheet
 * @param {*} scopeData
 * @returns
 */
exports.projectTrackerHeaderData = async (workSheet, scopeData) => {
  let currentColumn = 5;

  workSheet.mergeCells(1, 1, 2, 2);
  workSheet.getCell(1, currentColumn).value = ' ';

  workSheet.getCell(3, 1).value = global.constant.REPORT_LOCATION;
  workSheet.getCell(3, 2).value = global.constant.ASSET;
  workSheet.getCell(3, 3).value = global.constant.FROM;
  workSheet.getCell(3, 4).value = global.constant.TO;

  // Loop through scopeData to generate headers and sub-headers
  scopeData.forEach(scope => {
    const scopeName = scope.name;
    const reportCount = scope.reports.length;

    // Merge the first column (scope name) based on the number of reports
    workSheet.mergeCells(1, currentColumn, 1, currentColumn + reportCount - 1);
    workSheet.getCell(1, currentColumn).value = scopeName;
    workSheet.getCell(1, currentColumn).font = { bold: true };

    // Create sub-headers for reports
    scope.reports.forEach((report, index) => {
      const reportColumn = currentColumn + index;
      workSheet.getCell(2, reportColumn).value = `${report.title} (${report.type})`; // Report title
      workSheet.getCell(3, reportColumn).value = `${report.weightage.toFixed(2)}% `; // Report Weightage
      workSheet.getCell(2, reportColumn).font = { bold: true };
      workSheet.getCell(3, reportColumn).font = { bold: true };
    });

    // Move the currentColumn to next section
    currentColumn += reportCount;
  });
  for (let col = 1; col < currentColumn; col++) {
    workSheet.getCell(3, col).fill = commonUtils.cellColor;
    workSheet.getCell(3, col).font = { ...commonUtils.cellTextColor, bold: true };
    workSheet.getColumn(col).width = 30;
  }
  return currentColumn;
};

/**
 * Project tracker Report data
 *
 * @param {*} workSheet
 * @param {*} projectTrackerData
 * @returns
 */
exports.projectTrackerReportData = async (workSheet, projectTrackerData) => {
  let currentRow = 4;

  projectTrackerData.forEach(projectTracker => {
    const assetCount = projectTracker.assets.length;
    const hasAssets = assetCount > 0;
    const lastAssetRow = currentRow + assetCount - 1;

    // Handle location merging if assets exist
    if (hasAssets) {
      if (assetCount > 1) {
        workSheet.mergeCells(currentRow, 1, currentRow + assetCount - 1, 1);
      }
    }
    workSheet.getCell(currentRow, 1).value = projectTracker.locations;

    // Process assets if they exist, otherwise handle reports directly
    const assets = hasAssets ? projectTracker.assets : [null];
    assets.forEach((asset, index) => {
      // Fill the asset or location columns
      if (asset) {
        workSheet.getCell(currentRow, 2).value = `${asset.title}`;
        workSheet.getCell(currentRow, 3).value = `${asset.fromLocation.title}`;
        workSheet.getCell(currentRow, 4).value = ` ${asset.toLocation.title}`;
      }

      // Fill in report data for each report in reportList
      projectTracker.reportList.forEach((report, i) => {
        const reportListperAsset = report.reportList[index];

        if (reportListperAsset) {
          const completedDate = reportListperAsset?.completedDate;
          let completion = Number(reportListperAsset.completion);
          let isCheckedStatus = reportListperAsset.status.some(status =>
            [CHECKED, CLOSED].includes(status)
          );
          let cellValue = ' ';

          if (normalizeCompletion(completion) === 100 && isCheckedStatus) {
            cellValue = completedDate ? formatDate(completedDate) : ' ';
          } else if (completion > 0) {
            cellValue = `${completion.toFixed(2)}%`;
          }

          workSheet.getCell(currentRow, i + 5).value = cellValue;

          if (completion !== 0 || !reportListperAsset.isRequired) {
            workSheet.getCell(currentRow, i + 5).fill = handleCellBackground(completion);
          }
        } else {
          if (currentRow === lastAssetRow) {
            workSheet.mergeCells(currentRow - assetCount + 1, i + 5, currentRow, i + 5);
          }
        }
      });

      currentRow += 1;
    });
  });
  return currentRow;
};

/**
 * Project tracker Footer data
 *
 * @param {*} workSheet
 * @param {*} scopeData
 * @param {*} totalCompletions
 * @param {*} currentRow
 * @param {*} currentColumn
 * @returns
 */
exports.projectTrackerFooterData = async (
  workSheet,
  scopeData,
  totalCompletions,
  currentRow,
  currentColumn,
  projectData
) => {
  workSheet.unMergeCells(1, 1);
  workSheet.mergeCells(2, 1, 2, 2);

  workSheet.getCell(1, 1).value = projectData.title;
  workSheet.getCell(1, 1).fill = commonUtils.cellColor;
  workSheet.getCell(1, 1).font = { ...commonUtils.cellTextColor, bold: true };
  // Set local time in cell
  workSheet.getCell(1, 2).value = projectData.projectDate;
  workSheet.getCell(1, 2).fill = commonUtils.cellColor;
  workSheet.getCell(1, 2).font = { ...commonUtils.cellTextColor, bold: true };

  workSheet.mergeCells(currentRow, 1, currentRow, 4);

  workSheet.getCell(currentRow, 1).value = global.constant.COMPLETION;
  workSheet.getCell(currentRow, 1).fill = commonUtils.cellColor;
  workSheet.getCell(currentRow, 1).font = { ...commonUtils.cellTextColor, bold: true };
  let column = 5;

  scopeData.forEach(scope => {
    scope.reports.forEach(report => {
      workSheet.getCell(currentRow, column).value = report.reportCompletion
        ? `${report.reportCompletion.toFixed(2)}%`
        : global.constant.ZERO_PERCENTAGE;

      workSheet.getCell(currentRow, column).fill = handleCompletionCellColor(
        report.reportCompletion || 0
      );

      workSheet.getCell(currentRow, column).font = { bold: true };
      column += 1;
    });
  });

  workSheet.getCell(currentRow + 1, currentColumn - 2).value = global.constant.TOTAL_COMPLETION;
  workSheet.getCell(currentRow + 1, currentColumn - 2).font = { bold: true };
  workSheet.getCell(currentRow + 1, currentColumn - 1).value = `${totalCompletions.toFixed(2)}%`;
  workSheet.getCell(currentRow + 1, currentColumn - 1).fill =
    handleCompletionCellColor(totalCompletions);
  workSheet.getCell(currentRow + 1, currentColumn - 1).font = { bold: true };
};

/**
 * Generate training matrix excel
 *
 * @param {*} data
 * @param {*} res
 * @param {*} fileName
 * @param {*} personalDetails
 * @param {*} contractualDetails
 * @param {*} project
 * @param {*} account
 */
exports.getTrainingMatrixExcel = async (
  data,
  res,
  fileName,
  personalDetails,
  contractualDetails,
  project,
  account,
  timeZone = null
) => {
  try {
    const workBook = new ExcelJS.Workbook();
    const workSheet = workBook.addWorksheet(fileName);
    let updatedPersonalDetails = await this.addFieldInPersonalDetails(personalDetails);

    if (project && project !== 'all') {
      const certificate = await this.TrainingMatrixHeaderData(
        workSheet,
        data.requiredCertificate,
        updatedPersonalDetails,
        contractualDetails,
        project
      );

      await this.TrainingMatrixData(
        workSheet,
        data.trainingMatrix,
        certificate,
        updatedPersonalDetails,
        contractualDetails,
        project,
        timeZone
      );
    } else {
      let filterData = {
        account,
        deletedAt: null,
      };

      const response = await certificateTypeService.getCertificateType(filterData, null, null, 1);

      await this.TrainingMatrixHeaderData(
        workSheet,
        response,
        updatedPersonalDetails,
        contractualDetails,
        project
      );

      await this.TrainingMatrixData(
        workSheet,
        data.trainingMatrix,
        response,
        updatedPersonalDetails,
        contractualDetails,
        project,
        timeZone
      );
    }

    // generate XLS file and send it as response
    await this.sendXLSXResponse(res, workBook, fileName);
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * Generate training matrix excel header Data
 *
 * @param {*} workSheet
 * @param {*} requiredCertificate
 * @param {*} personalDetails
 * @param {*} contractualDetails
 * @param {*} project
 */
exports.TrainingMatrixHeaderData = async (
  workSheet,
  requiredCertificate,
  personalDetails,
  contractualDetails,
  project
) => {
  const certificates = [];
  let cell;

  cell = workSheet.getCell(1, 1);
  workSheet.getColumn(1).width = 20;
  colorCell(cell, 'Function', commonUtils.cellColor, { ...commonUtils.cellTextColor, bold: true });

  cell = workSheet.getCell(1, 2);
  workSheet.getColumn(2).width = 20;
  colorCell(cell, 'Name', commonUtils.cellColor, { ...commonUtils.cellTextColor, bold: true });

  let column = 3;

  if (project !== 'all') {
    const isrequiredCertificate = [];

    for (let certificate of requiredCertificate) {
      const certificateName = certificate.certificate.name;
      const certificateId = certificate.certificate._id.toString();

      if (!isrequiredCertificate.includes(certificateId)) {
        cell = workSheet.getCell(1, column);
        workSheet.getColumn(column).width = 20;

        colorCell(cell, certificateName, commonUtils.cellColor, {
          ...commonUtils.cellTextColor,
          bold: true,
        });

        isrequiredCertificate.push(certificateId);
        certificate.function = [certificate.function];

        certificates.push(certificate);

        column++;
      } else {
        certificates.forEach(item => {
          if (item.certificate._id.toString() === certificateId) {
            item.function.push(certificate.function);
          }
          return item;
        });
      }
    }
  } else {
    for (let certificate of requiredCertificate) {
      const certificateName = certificate.name;

      cell = workSheet.getCell(1, column);
      workSheet.getColumn(column).width = 20;
      colorCell(cell, certificateName, commonUtils.cellColor, {
        ...commonUtils.cellTextColor,
        bold: true,
      });

      column++;
    }
  }

  for (let key in personalDetails) {
    if (key === 'nextofKin' || key === 'address') {
      continue;
    }
    cell = workSheet.getCell(1, column);
    workSheet.getColumn(column).width = 20;

    const personalDetails = commonUtils.personalDetailsMapping[key];

    colorCell(cell, personalDetails, commonUtils.cellColor, {
      ...commonUtils.cellTextColor,
      bold: true,
    });
    column++;
  }
  for (let key in contractualDetails) {
    cell = workSheet.getCell(1, column);
    workSheet.getColumn(column).width = 20;

    const contractualDetail = commonUtils.contractualDetailsMapping[key];

    colorCell(cell, contractualDetail, commonUtils.cellColor, {
      ...commonUtils.cellTextColor,
      bold: true,
    });

    column++;
  }

  if (personalDetails.nextofKin) {
    await this.addColumns(workSheet, column);
  }

  return certificates;
};

/**
 * Generate training matrix excel body data
 *
 * @param {*} workSheet
 * @param {*} profileCertificates
 * @param {*} certificate
 * @param {*} personalDetails
 * @param {*} contractualDetails
 * @param {*} project
 */
exports.TrainingMatrixData = async (
  workSheet,
  profileCertificates,
  certificate,
  personalDetails,
  contractualDetails,
  project,
  timeZone = null
) => {
  let cell;
  let row = 2;
  // merge the function cell
  for (let projectKey in profileCertificates) {
    for (let functionKey in profileCertificates[projectKey]) {
      const functionLength = profileCertificates[projectKey][functionKey].data.length;

      if (functionLength > 0) {
        workSheet.mergeCells(row, 1, row + functionLength - 1, 1);
        cell = workSheet.getCell(row, 1);
        colorCell(cell, functionKey, {}, {}, commonUtils.alignment);
        row = row + functionLength;
      }
    }
  }

  const userCertificate = await trainingMatrixCommonData(profileCertificates);

  row = 2;
  for (
    let userCertificateIndex = 0;
    userCertificateIndex < userCertificate.length;
    userCertificateIndex++
  ) {
    let column = 2;
    cell = workSheet.getCell(row, column);
    colorCell(cell, userCertificate[userCertificateIndex].user, {}, {});
    column++;

    if (project !== 'all') {
      for (let certificateIndex = 0; certificateIndex < certificate.length; certificateIndex++) {
        // Find the matching certificate object
        const matchingCertificate = userCertificate[userCertificateIndex].certificate.find(
          item =>
            item.certificateType.toString() ===
            certificate[certificateIndex].certificate._id.toString()
        );

        cell = workSheet.getCell(row, column);

        if (matchingCertificate) {
          if (matchingCertificate.endDate === null) {
            colorCell(cell, global.constant.RIGHT_SYMBOL, {}, {}, commonUtils.alignment);
          } else {
            const color = checkCertificateExpiry(
              matchingCertificate.endDate,
              userCertificate[userCertificateIndex],
              certificate[certificateIndex],
              null,
              timeZone
            );
            colorCell(cell, formatDateByTimeZone(matchingCertificate.endDate, timeZone), color, {});
          }
        } else {
          const color = checkrequiredCertificate(
            userCertificate[userCertificateIndex],
            certificate[certificateIndex]
          );
          colorCell(cell, '', color, {});
        }

        column++;
      }
    } else {
      for (let certificateIndex = 0; certificateIndex < certificate.length; certificateIndex++) {
        // Find the matching certificate object
        const matchingCertificate = userCertificate[userCertificateIndex].certificate.find(
          item => item.certificateType.toString() === certificate[certificateIndex]._id.toString()
        );

        cell = workSheet.getCell(row, column);

        if (matchingCertificate) {
          if (matchingCertificate.endDate === null) {
            colorCell(cell, global.constant.RIGHT_SYMBOL, {}, {}, commonUtils.alignment);
          } else {
            const color = checkCertificateExpiry(
              matchingCertificate.endDate,
              userCertificate[userCertificateIndex],
              certificate[certificateIndex],
              project,
              timeZone
            );

            colorCell(cell, formatDateByTimeZone(matchingCertificate.endDate, timeZone), color, {});
          }
        } else {
          colorCell(cell, '', {}, {});
        }
        column++;
      }
    }

    const [userData] = await getUserProfileFunction({
      _id: userCertificate[userCertificateIndex].userId,
    });

    const contractualData = await getSingleRecord({
      userId: userCertificate[userCertificateIndex].userId,
    });

    const nextOfKins = await getKinByUserId(userCertificate[userCertificateIndex].userId);

    for (let data in personalDetails) {
      if (data === 'nextofKin' || data === 'address') {
        continue;
      }
      cell = workSheet.getCell(row, column);
      let userDetails;

      if (
        data === global.constant.CONTACT_NUMBER ||
        data === global.constant.DRIVINGLICENCE ||
        data === global.constant.SEAMANSBOOK
      ) {
        const formatedData = await changePersonalDetailsFormat(data, userData, timeZone);

        userDetails = formatedData ? formatedData : '';
      } else {
        userDetails = userData ? userData[`${data}`] : '';

        if (!userDetails) {
          userDetails = '';
        }
      }

      colorCell(cell, userDetails, {}, {});
      column++;
    }

    for (let data in contractualDetails) {
      cell = workSheet.getCell(row, column);
      let userContractData;

      if (data === global.constant.BIRTHDATE) {
        userContractData = contractualData ? formatDate(contractualData[`${data}`]) : '';
      } else if (data === global.constant.PASSPORT) {
        userContractData = contractualData?.passport || '';
      } else if (data === global.constant.PASSPORT_ISSUE_DATE) {
        // Get the primary passport's issue date (fromDate) from identityProof array
        userContractData = '';
        if (
          contractualData &&
          contractualData.identityProof &&
          contractualData.identityProof.length > 0
        ) {
          // Find the primary passport entry
          const primaryPassport = contractualData.identityProof.find(
            item => item.isPrimary === true
          );
          if (primaryPassport && primaryPassport.fromDate) {
            userContractData = await commonUtils.convertUTCToLocalTimezone(
              primaryPassport.fromDate,
              timeZone,
              global.constant.DATE_FORMAT_DD_MM_YYYY
            );
          }
        }
      } else if (data === global.constant.PASSPORT_EXPIRY_DATE) {
        // Get the primary passport's expiry date (toDate) from identityProof array
        userContractData = '';
        if (
          contractualData &&
          contractualData.identityProof &&
          contractualData.identityProof.length > 0
        ) {
          // Find the primary passport entry
          const primaryPassport = contractualData.identityProof.find(
            item => item.isPrimary === true
          );
          if (primaryPassport && primaryPassport.toDate) {
            userContractData = await commonUtils.convertUTCToLocalTimezone(
              primaryPassport.toDate,
              timeZone,
              global.constant.DATE_FORMAT_DD_MM_YYYY
            );
          }
        }
      } else if (data === global.constant.SECONDARY_PASSPORT) {
        userContractData = contractualData?.secondaryPassport || '';
      } else if (data === global.constant.SECONDARY_PASSPORT_ISSUE_DATE) {
        // Get the secondary passport's issue date (fromDate) from identityProof array
        userContractData = '';
        if (
          contractualData &&
          contractualData.identityProof &&
          contractualData.identityProof.length > 0
        ) {
          // Find the secondary passport entry
          const secondaryPassport = contractualData.identityProof.find(
            item => item.isSecondary === true
          );
          if (secondaryPassport && secondaryPassport.fromDate) {
            userContractData = await commonUtils.convertUTCToLocalTimezone(
              secondaryPassport.fromDate,
              timeZone,
              global.constant.DATE_FORMAT_DD_MM_YYYY
            );
          }
        }
      } else if (data === global.constant.SECONDARY_PASSPORT_EXPIRY_DATE) {
        // Get the secondary passport's expiry date (toDate) from identityProof array
        userContractData = '';
        if (
          contractualData &&
          contractualData.identityProof &&
          contractualData.identityProof.length > 0
        ) {
          // Find the secondary passport entry
          const secondaryPassport = contractualData.identityProof.find(
            item => item.isSecondary === true
          );
          if (secondaryPassport && secondaryPassport.toDate) {
            userContractData = await commonUtils.convertUTCToLocalTimezone(
              secondaryPassport.toDate,
              timeZone,
              global.constant.DATE_FORMAT_DD_MM_YYYY
            );
          }
        }
      } else if (data === global.constant.HEALTH_INSURANCE) {
        userContractData = contractualData?.healthInsuranceId || '';
      } else if (data === global.constant.HEALTH_INSURANCE_ISSUE_DATE) {
        userContractData =
          contractualData?.healthInsurance[0]?.fromDate !== undefined
            ? await commonUtils.convertUTCToLocalTimezone(
                contractualData?.healthInsurance[0]?.fromDate,
                timeZone,
                global.constant.DATE_FORMAT_DD_MM_YYYY
              )
            : '';
      } else if (data === global.constant.HEALTH_INSURANCE_EXPIRY_DATE) {
        userContractData =
          contractualData?.healthInsurance[0]?.toDate !== undefined
            ? await commonUtils.convertUTCToLocalTimezone(
                contractualData?.healthInsurance[0]?.toDate,
                timeZone,
                global.constant.DATE_FORMAT_DD_MM_YYYY
              )
            : '';
      } else if (data === global.constant.LIABILITY_INSURANCE) {
        userContractData = contractualData?.liabilityInsuranceId || '';
      } else if (data === global.constant.LIABILITY_INSURANCE_ISSUE_DATE) {
        userContractData =
          contractualData?.liabilityInsurance[0]?.fromDate !== undefined &&
          contractualData?.liabilityInsurance[0]?.fromDate !== null
            ? await commonUtils.convertUTCToLocalTimezone(
                contractualData?.liabilityInsurance[0].fromDate,
                timeZone,
                global.constant.DATE_FORMAT_DD_MM_YYYY
              )
            : '';
      } else if (data === global.constant.LIABILITY_INSURANCE_EXPIRY_DATE) {
        userContractData =
          contractualData?.liabilityInsurance[0]?.toDate !== undefined &&
          contractualData?.liabilityInsurance[0]?.toDate !== null
            ? await commonUtils.convertUTCToLocalTimezone(
                contractualData?.liabilityInsurance[0].toDate,
                timeZone,
                global.constant.DATE_FORMAT_DD_MM_YYYY
              )
            : '';
      } else {
        userContractData = contractualData ? contractualData[`${data}`] : '';
      }

      if (!userContractData) {
        userContractData = '';
      }

      colorCell(cell, userContractData, {}, {});
      column++;
    }

    if (personalDetails.nextofKin) {
      for (let kin of nextOfKins) {
        let nextkinData;

        for (let data of commonUtils.nextOfKinData) {
          cell = workSheet.getCell(row, column);
          if (data === global.constant.KIN_CONTACT_NUMBER) {
            const formatedData = await changePersonalDetailsFormat(data, kin);

            nextkinData = formatedData ? formatedData : '';
          } else {
            nextkinData = kin ? kin[data] : '';
          }
          if (!nextkinData) {
            nextkinData = '';
          }

          colorCell(cell, nextkinData, {}, {});
          column++;
        }
      }
    }
    row++;
  }
};

/**
 * add columns in personal Details
 *
 * @param {*} workSheet
 * @param {*} column
 */
exports.addColumns = async (workSheet, column) => {
  let cell;

  for (let nextKin = 0; nextKin <= 2; nextKin++) {
    for (let userDetail of commonUtils.nextofKin) {
      cell = workSheet.getCell(1, column);
      workSheet.getColumn(column).width = 20;
      colorCell(cell, userDetail, commonUtils.cellColor, {
        ...commonUtils.cellTextColor,
        bold: true,
      });
      column++;
    }
  }

  return column;
};

/**
 * add field in personal Details
 *
 * @param {*} personalDetails

 */
exports.addFieldInPersonalDetails = async personalDetails => {
  let updatedPersonalDetails = { ...personalDetails };
  const additionalFields = {
    callingName: [
      ['firstName', true],
      ['lastName', true],
    ],
    address: [
      ['street', true],
      ['area', true],
      ['zipCode', true],
      ['city', true],
      ['state', true],
      ['country', true],
    ],
    prefAirportDeprt: [['secondaryPrefAirportDeprt', true]],
    travelTimeToAirport: [['travelTimeToSecondAirport', true]],
  };

  for (const personalDetail in personalDetails) {
    if (personalDetails[personalDetail] && additionalFields[personalDetail]) {
      updatedPersonalDetails = Object.fromEntries(
        Object.entries(updatedPersonalDetails).flatMap(([key, value]) =>
          key === personalDetail
            ? [[key, value], ...additionalFields[personalDetail]]
            : [[key, value]]
        )
      );
    }
  }
  return updatedPersonalDetails;
};

/**
 * export inventory excel
 *
 * @param {*} res
 * @param {*} fileName
 * @param {*} columns
 * @param {*} rows
 * @param {*} timeZone
 * @returns
 */
exports.exportInventoryExcel = async (res, fileName, columns, rows, timeZone = null) => {
  try {
    timeZone = timeZone || global.constant.DEFAULT_TIMEZONE;

    const workBook = new ExcelJS.Workbook();
    const workSheet = workBook.addWorksheet(fileName);

    // add columns
    workSheet.columns = columns;

    // add rows
    Object.keys(rows).forEach(key => {
      workSheet.addRow(rows[key]);
    });
    for (let column = 1; column <= columns.length; column++) {
      let cell = workSheet.getCell(1, column);
      colorCell(cell, null, commonUtils.cellColor, {
        ...commonUtils.cellTextColor,
        bold: true,
      });
    }

    for (let row = 2; row <= rows.length + 1; row++) {
      let cell = workSheet.getCell(row, 8);

      if (cell.value) {
        const color = checkInventoryExpiry(cell.value, timeZone);
        colorCell(cell, formatDateByTimeZone(cell.value, timeZone), color, {});
      }
    }

    // generate XLS file and send it as response
    await this.sendXLSXResponse(res, workBook, fileName);
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};

/**
 * send excel response
 *
 * @param {*} res
 * @param {*} workBook
 * @param {*} fileName
 * @returns
 */
exports.sendXLSXResponse = async (res, workBook, fileName) => {
  res.setHeader(
    'Content-Type',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  );
  res.setHeader('Content-Disposition', `attachment; filename=${fileName}.xlsx`);

  workBook.xlsx
    .write(res)
    .then(() => {
      res.end();
    })
    .catch(error => {
      res.status(500).json(responseUtils.errorResponse(error.message));
    });
};

/**
 * export dpr excel
 *
 * @param {*} res
 * @param {*} data
 */
exports.exportDprExcel = async (res, data) => {
  try {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('DPR Report');

    // Define columns
    worksheet.columns = commonUtils.dprTableHeaders;

    // Add rows
    data.dprs.forEach(item => {
      worksheet.addRow({
        id: item._id.toString(),
        version: item.version,
        dprNo: item.dprNo || 'N/A',
        status: item.status,
        project: item?.project?.title,
        dprDate: item.dprDate ? new Date(item.dprDate).toISOString().split('T')[0] : 'N/A',
        createdBy: item.createdBy
          ? `${item?.createdBy?.firstName || item?.createdBy?.callingName} ${
              item?.createdBy?.lastName
            }`
          : 'N/A',
        createdAt: new Date(item.createdAt).toISOString().split('T')[0],
      });
    });

    await this.sendXLSXResponse(res, workbook, 'DPR_Report');
  } catch (error) {
    res.status(500).json(responseUtils.errorResponse(error.message));
  }
};
