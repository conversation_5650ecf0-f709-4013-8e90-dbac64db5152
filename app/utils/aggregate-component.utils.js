exports.aggregateLookup = component => {
  switch (component) {
    case 'project':
      return {
        $lookup: {
          from: 'projects',
          localField: 'project',
          foreignField: '_id',
          pipeline: [{ $project: { _id: 1, title: 1 } }],
          as: 'project',
        },
      };
    case 'createdBy':
      return {
        $lookup: {
          from: 'users',
          localField: 'createdBy',
          foreignField: '_id',
          pipeline: [
            { $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1, contactNumber: 1 } },
          ],
          as: 'createdBy',
        },
      };
    case 'updatedBy':
      return {
        $lookup: {
          from: 'users',
          localField: 'updatedBy',
          foreignField: '_id',
          pipeline: [
            { $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1, contactNumber: 1 } },
          ],
          as: 'updatedBy',
        },
      };
    case 'account':
      return {
        $lookup: {
          from: 'accounts',
          localField: 'account',
          foreignField: '_id',
          pipeline: [{ $project: { _id: 1, name: 1 } }],
          as: 'account',
        },
      };
    case 'team':
      return {
        $lookup: {
          from: 'teams',
          localField: 'team',
          foreignField: '_id',
          pipeline: [
            {
              $project: {
                _id: 1,
                teamsWfmName: 1,
              },
            },
          ],
          as: 'team',
        },
      };
    case 'teammembers':
      return {
        $lookup: {
          from: 'teammembers',
          let: { shiftId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [{ $eq: ['$shift', '$$shiftId'] }, { $eq: ['$deletedAt', null] }],
                },
              },
            },
            {
              $lookup: {
                from: 'members',
                let: { memberId: '$member', functionId: '$function' },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $and: [{ $eq: ['$_id', '$$memberId'] }],
                      },
                    },
                  },
                  {
                    $lookup: {
                      from: 'users',
                      let: { userId: '$user' },
                      pipeline: [
                        {
                          $match: {
                            $expr: {
                              $eq: ['$_id', '$$userId'],
                            },
                          },
                        },
                        {
                          $project: {
                            _id: 0,
                            callingName: 1,
                            firstName: 1,
                            lastName: 1,
                          },
                        },
                      ],
                      as: 'userDetails',
                    },
                  },
                  {
                    $unwind: {
                      path: '$userDetails',
                      preserveNullAndEmptyArrays: true,
                    },
                  },
                  {
                    $lookup: {
                      from: 'functions',
                      let: { funcId: '$function' },
                      pipeline: [
                        {
                          $match: {
                            $expr: {
                              $eq: ['$_id', '$$funcId'],
                            },
                          },
                        },
                        {
                          $project: {
                            _id: 0,
                            functionName: 1,
                          },
                        },
                      ],
                      as: 'functionDetails',
                    },
                  },
                  {
                    $unwind: {
                      path: '$functionDetails',
                      preserveNullAndEmptyArrays: true,
                    },
                  },
                  {
                    $project: {
                      _id: 1,
                      callingName: '$userDetails.callingName',
                      firstName: '$userDetails.firstName',
                      lastName: '$userDetails.lastName',
                      function: '$functionDetails.functionName',
                    },
                  },
                ],
                as: 'memberDetails',
              },
            },
            {
              $unwind: {
                path: '$memberDetails',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $project: {
                shift: 1,
                _id: '$memberDetails._id',
                callingName: '$memberDetails.callingName',
                firstName: '$memberDetails.firstName',
                lastName: '$memberDetails.lastName',
                function: '$memberDetails.function',
              },
            },
          ],
          as: 'teammembers',
        },
      };

    case 'shift-activities':
      return {
        $lookup: {
          from: 'shift-activities',
          let: { shiftId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [{ $eq: ['$shift', '$$shiftId'] }, { $eq: ['$deletedAt', null] }],
                },
              },
            },
            {
              $lookup: {
                from: 'activities',
                let: { activityId: '$activity' },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $eq: ['$_id', '$$activityId'],
                      },
                    },
                  },
                  {
                    $project: {
                      _id: 1,
                      name: 1,
                    },
                  },
                ],
                as: 'activityDetails',
              },
            },
            {
              $unwind: {
                path: '$activityDetails',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $lookup: {
                from: 'locations',
                let: { locationId: '$location' },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $eq: ['$_id', '$$locationId'],
                      },
                    },
                  },
                  {
                    $project: {
                      title: 1,
                    },
                  },
                ],
                as: 'locationDetails',
              },
            },
            {
              $unwind: {
                path: '$locationDetails',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $project: {
                _id: 1,
                comments: 1,
                endTime: 1,
                activity: '$activityDetails.name',
                location: '$locationDetails.title',
              },
            },
          ],
          as: 'shiftActivities',
        },
      };
    case 'location':
      return {
        $lookup: {
          from: 'locations',
          localField: 'location',
          foreignField: '_id',
          pipeline: [{ $project: { _id: 1, title: 1 } }],
          as: 'location',
        },
      };
    case 'hostedBy':
      return {
        $lookup: {
          from: 'users',
          localField: 'hostedBy',
          foreignField: '_id',
          pipeline: [
            { $project: { _id: 1, callingName: 1, firstName: 1, lastName: 1, contactNumber: 1 } },
          ],
          as: 'hostedBy',
        },
      };
    case 'memberSignatureUser':
      return {
        $lookup: {
          from: 'users',
          localField: 'memberSignature.user',
          foreignField: '_id',
          pipeline: [{ $project: { callingName: 1, firstName: 1, lastName: 1 } }],
          as: 'userDetails',
        },
      };
  }
};

exports.aggregateUnwind = component => {
  switch (component) {
    case 'project':
      return { $unwind: '$project' };
    case 'createdBy':
      return { $unwind: '$createdBy' };
    case 'updatedBy':
      return { $unwind: '$updatedBy' };
    case 'account':
      return { $unwind: '$account' };
    case 'team':
      return { $unwind: '$team' };
    case 'location':
      return { $unwind: '$location' };
    case 'hostedBy':
      return { $unwind: '$hostedBy' };
  }
};
