const { toObjectId } = require('./common.utils');
const modifyDefaultDataUtils = require('./modify-default-data.utils');
const axios = require('axios');
const dateFormat = require('dateformat');

// services
const shiftServices = require('../services/shift.service');
const dprService = require('../services/dpr.service');
const syncApiService = require('../services/sync-api.service');
const syncApiManageService = require('../services/sync-api-manage.service');

// utils
const commonUtils = require('./common.utils');

exports.getFilterAndPaginationParams = async req => {
  let page = req.query.page ?? null;
  let perPage = req.query.perPage ?? null;
  let sort = req.query.sort && req.query.sort === 'asc' ? 1 : -1;

  let filter = req.query;
  let removeKeys = ['page', 'perPage', 'sort'];
  filter = await commonUtils.filterParamsModify(filter, removeKeys);

  for (let key in req.query) {
    if (req.query[key]) {
      filter[key] = commonUtils.isValidId(req.query[key])
        ? toObjectId(req.query[key])
        : req.query[key];
    }
  }

  filter.account = req.userData.account;
  filter.deletedAt = null;

  if (filter.asset) {
    filter.asset = { $elemMatch: { asset: filter.asset } };
  } else {
    delete filter.asset;
  }

  return { filter, page, perPage, sort };
};

exports.shiftActivityOtherProject = async (shiftData, reqData, req) => {
  // other project
  // activity
  if (shiftData.project.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
    reqData.activity = await modifyDefaultDataUtils.createDefaultActivity(
      reqData.activity,
      req.userData.account,
      shiftData.project._id
    );
  }
  // activity

  // location
  if (shiftData.project.defaultIdentifier == global.constant.DEFAULT_DATA_IDENTIFIER) {
    req.body.project = shiftData.project._id;
    reqData.location = await modifyDefaultDataUtils.createDefaultlocation(req);
  }

  // location
  // end other project
};

exports.certificateType = [
  {
    path: 'account',
    select: { _id: 1, name: 1 },
    options: { strictPopulate: false },
  },
  {
    path: 'createdBy',
    model: 'user',
    select: { _id: 1, name: 1 },
    populate: {
      path: 'role',
      select: { title: 1 },
    },
  },
  {
    path: 'updatedBy',
    model: 'user',
    select: { _id: 1, name: 1 },
    populate: {
      path: 'role',
      select: { title: 1 },
    },
  },
  {
    path: 'deletedBy',
    model: 'user',
    select: { _id: 1, name: 1 },
    populate: {
      path: 'role',
      select: { title: 1 },
    },
  },
];

exports.cirtificate = [
  {
    path: 'project project',
    select: { title: 1, _id: 1 },
    strictPopulate: false,
  },
  {
    path: 'account account',
    select: { name: 1, _id: 1 },
    strictPopulate: false,
  },
  {
    path: 'certificates',
    select: { name: 1, _id: 1 },
    model: 'certificate-type',
  },
  {
    path: 'function function',
    select: { functionName: 1, _id: 1 },
    strictPopulate: false,
  },
];

exports.convertImageUrlToBase64Image = async url => {
  try {
    const response = await axios.get(url, { responseType: 'arraybuffer' });
    const base64Image =
      `data:${response.headers['content-type']};base64,` +
      Buffer.from(response.data).toString('base64');
    return base64Image;
  } catch (error) {
    return null;
  }
};

/**
 * Format Date to DD-MM-YYYY HH:mm:ss
 *
 * @param {*} date
 * @returns
 */
exports.formatDateTime = async date => {
  date = new Date(date);
  const padZero = num => (num < 10 ? '0' : '') + num;

  const day = padZero(date.getDate());
  const month = padZero(date.getMonth() + 1);
  const year = date.getFullYear();
  const hours = padZero(date.getHours());
  const minutes = padZero(date.getMinutes());

  return `${day}-${month}-${year} ${hours}:${minutes}`;
};

/**
 * Format Date to DD-MM-YYYY
 *
 * @param {*} date
 * @returns
 */
exports.formatDate = date => {
  date = new Date(date);
  const padZero = num => (num < 10 ? '0' : '') + num;

  const day = padZero(date.getDate());
  const month = padZero(date.getMonth() + 1);
  const year = date.getFullYear();

  return `${day}-${month}-${year}`;
};

/**
 * Format Date to IST DD-MM-YYYY HH:mm:ss
 *
 * @param {*} date
 * @returns
 */

exports.formatDateUTC = async date => {
  const utcDate = new Date(date);
  const utcTime = utcDate.getTime();
  const offsetTime = 5.5 * 60 * 60 * 1000;
  const istDate = new Date(utcTime + offsetTime);
  const formatDate = dateFormat(istDate, global.constant.PDF_DATE_FORMAT);
  return formatDate;
};

/**
 * Calculate Time Difference In hours
 *
 * @param {*} startDate
 * @param {*} endDate
 * @returns
 */
exports.calculateDurationsHours = async (data, startTime) => {
  let previousEndTime = new Date(startTime);
  return data.map(entry => {
    const activityStartTime = new Date(previousEndTime);
    let currentEndTime = new Date(entry.endTime);
    let durationDifference = currentEndTime - previousEndTime;

    // Convert to minutes, hours, etc.
    const diffInMinutes = Math.floor(durationDifference / (1000 * 60));
    const hours = diffInMinutes / 60;

    // Update previousEndTime for next iteration
    previousEndTime = currentEndTime;

    return {
      ...entry,
      activityStartTime: activityStartTime,
      duration: { hours: hours.toFixed(2) },
    };
  });
};

/**
 * Calculate Activity Duration
 *
 * @param {*} shiftActivities
 * @param {*} shiftData
 * @returns
 */
exports.calculateActivityDuration = async (shiftActivities, shiftData) => {
  try {
    if (shiftActivities.length) {
      let shiftActivityDuration = await this.calculateDurations(
        shiftActivities,
        shiftData.startDate
      );

      let duration = await this.sumDurations(shiftActivityDuration);

      await shiftServices.updateShift(shiftData._id, {
        duration: `${duration.hours ?? 0}:${duration.minutes ?? 0}`,
      });
    }

    return true;
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Calculate Time Difference
 *
 * @param {*} startDate
 * @param {*} endDate
 * @returns
 */
exports.calculateDurations = async (data, startTime) => {
  /** Sort shift activity according to end time */
  data.sort((previous, next) => new Date(previous.endTime) - new Date(next.endTime));

  let previousEndTime = new Date(startTime);
  return data.map(entry => {
    let currentEndTime = new Date(entry.endTime);
    let durationDifference = currentEndTime - previousEndTime;

    // Convert to minutes, hours, etc.
    const diffInMinutes = Math.floor(durationDifference / (1000 * 60));
    const hours = Math.floor(diffInMinutes / 60);
    const minutes = diffInMinutes % 60;

    // Update previousEndTime for next iteration
    previousEndTime = currentEndTime;

    return {
      ...entry,
      duration: { hours, minutes },
    };
  });
};

/**
 * Calculate Total Shift Duration
 *
 * @param {*} data
 * @returns
 */
exports.sumDurations = async data => {
  let totalMinutes = 0;

  data.forEach(entry => {
    totalMinutes += entry.duration.hours * 60 + entry.duration.minutes;
  });

  // Convert total minutes to hours and minutes
  const totalHours = Math.floor(totalMinutes / 60);
  const remainingMinutes = totalMinutes % 60;

  return {
    hours: Math.abs(totalHours),
    ...(remainingMinutes > 0 && { minutes: Math.abs(remainingMinutes) }),
  };
};

/**
 * Get search and sort
 *
 * @param {*} req
 * @param {*} project
 * @param {*} account
 * @param {*} title
 */
exports.getSearchAndSort = async (req, project, account, title) => {
  let page = req.query.page ? req.query.page : '';
  let perPage = req.query.perPage ? req.query.perPage : '';
  let sort;

  if (!req.query?.sortOrder) {
    sort = req.query.sort && req.query.sort === 'asc' ? { createdAt: 1 } : { createdAt: -1 };
  } else {
    sort = req.query.sortOrder === 'asc' ? { [title]: 1 } : { [title]: -1 };
  }

  let filterData = {
    account: account,
    deletedAt: null,
    project,
    ...(req.query.name && { [title]: await commonUtils.convertToCaseInsensetive(req.query.name) }),
  };
  return { page, perPage, sort, filterData };
};

/**
 * Get Table Data
 * @param {*} shifts
 * @returns
 */
exports.getTableData = async (dataList, tableHeaders, rowMapper) => {
  let tableData = {};
  let defaultRowData = [];

  // Map data list to rows based on the rowMapper
  for (const item of dataList) {
    let row = await rowMapper(item);
    defaultRowData.push(row);
  }
  tableData.columns = [...tableHeaders];
  // Table rows for excel
  tableData.rows = [...defaultRowData];
  return tableData;
};

exports.urlReplacement = async url => url.replace(global.constant.LAST_SLASH, '/compressed/$1');

/**
 * Normalize Completion
 *
 * @param {*} value
 * @returns
 */
exports.normalizeCompletion = value => {
  const numValue = Number(value ?? 0);
  return Math.round(numValue * 100) / 100;
};

/**
 * Get background color
 * @param {*} value
 * @returns
 */
exports.handleCellBackground = value => {
  const numericValue = this.normalizeCompletion(value);

  if (numericValue === 0) {
    return {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFe2e8f0' }, // Grey background color
    };
  }
  if (numericValue <= 50) {
    return {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFFFCCCC' }, // Pink background color
    };
  }
  if (numericValue <= 75) {
    return {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFFBE4D5' }, // Light Pink background color
    };
  }
  if (numericValue < 100) {
    return {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFFEF2CB' }, // Light Orage background color
    };
  }
  if (numericValue === 100) {
    return {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE2EFD9' }, // Light Green background color
    };
  }
};

/**
 * Get completion background color
 * @param {*} value
 * @returns
 */
exports.handleCompletionCellColor = value => {
  const numericValue = this.normalizeCompletion(value);

  if (numericValue <= 50) {
    return {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFFFCCCC' }, // Pink background color
    };
  }
  if (numericValue <= 75) {
    return {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFFBE4D5' }, // Light Pink background color
    };
  }
  if (numericValue < 100) {
    return {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFFEF2CB' }, // Light Orage background color
    };
  }
  if (numericValue === 100) {
    return {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE2EFD9' }, // Light Green background color
    };
  }
};

/**
 * Get structure data of training matrix
 *
 * @param {*} profileCertificates
 */
exports.trainingMatrixCommonData = async profileCertificates => {
  const userCertificate = [];
  for (let projectKey in profileCertificates) {
    for (let functionKey in profileCertificates[projectKey]) {
      const data = profileCertificates[projectKey][functionKey]['data'];

      for (let user of Object.keys(data)) {
        const userObj = data[user];

        const obj = {
          functionName: functionKey,
          functionId: profileCertificates[projectKey][functionKey].id,
          certificate: userObj.userCertificates,
          user: userObj.name,
          userId: userObj._id,
        };
        userCertificate.push(obj);
      }
    }
  }
  return userCertificate;
};

/**
 * Edit the cell properties of excel
 *
 * @param {*} cell
 * @param {*} value
 * @param {*} fill
 * @param {*} font
 * @param {*} alignment
 */
exports.colorCell = (cell, value = null, fill = {}, font = {}, alignment = {}) => {
  if (value) {
    cell.value = value;
  }

  if (fill && Object.keys(fill).length > 0) {
    cell.fill = fill;
  }

  if (font && Object.keys(font).length > 0) {
    cell.font = font;
  }

  if (alignment && Object.keys(alignment).length > 0) {
    cell.alignment = alignment;
  }
};

/**
 * check certifiacte expiry date
 *
 * @param {*} expiryDate
 * @param {*} userCertificate
 * @param {*} certificate
 * @param {*} project
 */
exports.checkCertificateExpiry = (
  expiryDate,
  userCertificate,
  certificate,
  project,
  timeZone = global.constant.DEFAULT_TIMEZONE
) => {
  if (project !== 'all') {
    const isRequired = certificate.function.find(
      item => item._id.toString() === userCertificate.functionId.toString()
    );

    if (!isRequired) {
      return global.constant.LIGHT_GREY_BACKGROUND;
    }
  }
  const expiryDateObj = this.getCurrentTimeInTimezone(timeZone, expiryDate);
  const currentDate = this.getCurrentTimeInTimezone(timeZone);

  // Calculate the difference in days
  const timeDifference = expiryDateObj - currentDate; // Difference in milliseconds
  const daysRemaining = Math.floor(timeDifference / global.constant.DAY_CONVERTOR); // Convert ms to days

  let color = {};

  switch (true) {
    case daysRemaining <= global.constant.DAY_REMAINING_LESS_THAN_ZERO:
      color = global.constant.LIGHT_RED_BACKGROUND;
      break;
    case daysRemaining <= global.constant.DAY_REMAINING_LESS_THAN_THIRTY:
      color = global.constant.LIGHT_ORANGE_BACKGROUND;
      break;
    case daysRemaining <= global.constant.DAY_REMAINING_LESS_THAN_SIXTY:
      color = global.constant.LIGHT_YELLOW_BACKGROUND;
      break;
    default:
      break;
  }

  return color;
};

/**
 * check required certificate
 *
 * @param {*} userCertificate
 * @param {*} certificate
 */
exports.checkrequiredCertificate = (userCertificate, certificate) => {
  const isRequired = certificate.function.find(
    item => item._id.toString() === userCertificate.functionId.toString()
  );

  if (isRequired) {
    return global.constant.LIGHT_RED_BACKGROUND;
  } else {
    return global.constant.LIGHT_GREY_BACKGROUND;
  }
};

exports.changePersonalDetailsFormat = async (
  data,
  userData,
  timeZone = global.constant.DEFAULT_TIMEZONE
) => {
  if (data === global.constant.PASSPORT) {
    return userData?.passport || '';
  } else if (data === global.constant.SECONDARY_PASSPORT) {
    return userData?.secondaryPassport || '';
  } else if (data === global.constant.CONTACT_NUMBER) {
    // structure contact number
    return `${userData.contactNumber.in} ${userData.contactNumber.number}`;
  } else if (data === global.constant.KIN_CONTACT_NUMBER) {
    return `${userData.kinContactNumber.in} ${userData.kinContactNumber.number}`;
  } else if (data === global.constant.DRIVINGLICENCE) {
    // structure contact number
    if (userData.drivingLicence.length > 0) {
      return `${
        userData.drivingLicence[0].licenseNumber
      } (${await commonUtils.convertUTCToLocalTimezone(
        userData.drivingLicence[0].issueDate,
        timeZone,
        global.constant.DATE_FORMAT_DD_MM_YYYY
      )} - ${await commonUtils.convertUTCToLocalTimezone(
        userData.drivingLicence[0].expiryDate,
        timeZone,
        global.constant.DATE_FORMAT_DD_MM_YYYY
      )})`;
    } else {
      return '';
    }
  } else if (data === global.constant.SEAMANSBOOK) {
    if (userData.seamansBook.length > 0) {
      return `${userData.seamansBook[0].name} (${await commonUtils.convertUTCToLocalTimezone(
        userData.seamansBook[0].fromDate,
        timeZone,
        global.constant.DATE_FORMAT_DD_MM_YYYY
      )} - ${await commonUtils.convertUTCToLocalTimezone(
        userData.seamansBook[0].toDate,
        timeZone,
        global.constant.DATE_FORMAT_DD_MM_YYYY
      )})`;
    } else {
      return '';
    }
  }
};

/**
 * Get current time in timezone
 *
 * @param {*} timezone
 * @returns
 */
exports.getCurrentTimeInTimezone = (timezone, expiryDate = null) => {
  let now;
  if (expiryDate) {
    now = new Date(expiryDate);
  } else {
    now = new Date();
  }

  // Format the date using the given timezone
  const options = {
    timeZone: timezone,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    fractionalSecondDigits: 3,
    hourCycle: 'h23',
  };

  const formatter = new Intl.DateTimeFormat('en-US', options);
  const formattedDate = formatter.formatToParts(now);

  // Convert formatted parts into ISO format
  const dateParts = {
    year: '',
    month: '',
    day: '',
    hour: '',
    minute: '',
    second: '',
    fractionalSecond: '',
  };

  formattedDate.forEach(({ type, value }) => {
    if (type in dateParts) {
      dateParts[type] = value;
    }
  });

  return new Date(
    `${dateParts.year}-${dateParts.month}-${dateParts.day}T${dateParts.hour}:${dateParts.minute}:${dateParts.second}.${dateParts.fractionalSecond}Z`
  );
};

/**
 * validate name input
 *
 * @param {*} name
 * @returns {string|boolean}
 */
exports.validateNameInput = async name => {
  const updatedName = name.replace(global.constant.ESCAPE_CHARACTERS, '\\$&'); // Escape special characters

  if (typeof updatedName !== 'string' || updatedName.length > 100) {
    return false;
  }
  return updatedName;
};

/**
 * validate search input
 *
 * @param {*} search
 * @returns {string|boolean}
 */
exports.validateSearch = async search => {
  if (!search) {
    return '';
  } else if (typeof search === 'string') {
    search = search.trim();
    return search;
  }

  return false;
};

/**
 * generate dpr number
 *
 * @param {*} project
 * @param {*} account
 * @returns
 */
exports.generateDprNumber = async (project, account) => {
  const latestDpr = await dprService.getLatestDpr({
    account,
    project: project._id,
    deletedAt: null,
  });

  let incrementNumber = 1;

  if (latestDpr) {
    const latestNumber = parseInt(latestDpr.split('-')[0].trim(), 10);
    incrementNumber = latestNumber + 1;
  }

  const formattedNumber = incrementNumber.toString().padStart(3, '0');

  return `${formattedNumber} - ${project.title}`;
};

/**
 * Format Date Time Zone to DD-MM-YYYY
 *
 * @param {*} date
 * @param {*} timezone
 * @returns
 */
exports.formatDateByTimeZone = (date, timezone = global.constant.DEFAULT_TIMEZONE) => {
  const newdate = new Date(date);

  const options = {
    timeZone: timezone,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    fractionalSecondDigits: 3,
    hourCycle: 'h23',
  };

  const formatter = new Intl.DateTimeFormat('en-US', options);
  const formattedDate = formatter.formatToParts(newdate);

  // Convert formatted parts into ISO format
  const dateParts = {
    year: '',
    month: '',
    day: '',
    hour: '',
    minute: '',
    second: '',
    fractionalSecond: '',
  };

  formattedDate.forEach(({ type, value }) => {
    if (type in dateParts) {
      dateParts[type] = value;
    }
  });

  return `${dateParts.day}-${dateParts.month}-${dateParts.year}`;
};

/**
 * Format Total Time
 *
 * @param {*} totalMinutes
 * @returns
 */
exports.formatTotalTime = totalMinutes => {
  const hrs = Math.floor(totalMinutes / 60);
  const mins = totalMinutes % 60;
  return `${hrs}h ${mins}m`;
};

/**
 * check certifiacte expiry date
 *
 * @param {*} expiryDate
 * @param {*} timeZone
 */
exports.checkInventoryExpiry = (expiryDate, timeZone) => {
  const expiryDateObj = this.getCurrentTimeInTimezone(timeZone, expiryDate);
  const currentDate = this.getCurrentTimeInTimezone(timeZone);

  // Calculate the difference in days
  const timeDifference = expiryDateObj - currentDate; // Difference in milliseconds
  const daysRemaining = Math.floor(timeDifference / global.constant.DAY_CONVERTOR); // Convert ms to days

  let color = {};

  switch (true) {
    case daysRemaining <= global.constant.DAY_REMAINING_LESS_THAN_ZERO:
      color = global.constant.LIGHT_RED_BACKGROUND;
      break;
    case daysRemaining <= global.constant.DAY_REMAINING_LESS_THAN_THIRTY:
      color = global.constant.LIGHT_ORANGE_BACKGROUND;
      break;
    case daysRemaining <= global.constant.DAY_REMAINING_LESS_THAN_SIXTY:
      color = global.constant.LIGHT_YELLOW_BACKGROUND;
      break;
    default:
      break;
  }

  return color;
};

/**
 * Get start and end dates
 *
 * @param {Date} date
 * @returns
 */
exports.getStartAndEndDates = date => {
  const startDprDate = new Date(date);
  const endDprDate = new Date(startDprDate.getTime() + global.constant.DAY_CONVERTOR);

  return { startDprDate, endDprDate };
};

/**
 * Get day wise date
 *
 * @param day
 * @returns
 */
exports.getDateFilter = async day => {
  const now = new Date();
  let startDate = null;
  const firstDayOfWeek = now.getDate() - now.getDay();
  switch (day) {
    case global.constant.TODAY:
      startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      break;
    case global.constant.WEEK:
      startDate = new Date(now.getFullYear(), now.getMonth(), firstDayOfWeek);
      break;
    case global.constant.MONTH:
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      break;
    case global.constant.ALL:
    default:
      startDate = null; // No date filter
      break;
  }
  return startDate;
};

/**
 * Update sync api manage data - hashKey and updatedAt
 *
 * @param {*} requestData
 */
exports.updateSyncApiManage = async requestData => {
  const { syncApis, account } = requestData;
  for (let syncApi = 0; syncApi < syncApis.length; syncApi++) {
    const apiName = this.syncApisCollections(syncApis[syncApi]);
    if (apiName) {
      const syncApi = await syncApiService.findOneSyncApi({ apiName });
      if (syncApi) {
        const hashKey = commonUtils.generateOrderNumber(10, 'sync');
        await syncApiManageService.updateSyncApiManage(
          {
            syncApi: syncApi._id,
            account,
          },
          { $set: { hashKey, updatedAt: new Date() } }
        );
      }
    }
  }
};

/**
 * Get sync api collections
 *
 * @param {*} args
 * @returns
 */
exports.syncApisCollections = args => {
  const collections = {
    mainConfig: 'files/config',
    toolboxConfig: 'files/toolbox-talk',
    reportConfig: 'files/report',
    reportUsers: 'report/users?type=user&status=open&projectStatus=open',
    reportNewFormConfig: 'reports/config/new-form',
    members: 'members?isActive=true',
    allUserReports: 'report/users/all-user-reports',
    shifts: 'shifts?status=open&projectStatus=open&project=all',
    users: 'users?isActive=true',
    shiftActivities: 'shifts/activities',
    equipmentConfig: 'files/equipment',
    projects: 'projects?status=open&isActive=true',
    projectDocuments: 'project-documents?status=open',
    upoloadCertificate: 'upload-certificate/certificates',
    userProfile: 'users/profile?type=mobile',
    nationalities: 'nationality',
  };
  return collections[args] ?? null;
};
